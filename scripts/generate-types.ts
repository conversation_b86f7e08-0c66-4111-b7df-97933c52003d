import * as fs from "fs";
import { generateZodClientFromOpenAPI } from "openapi-zod-client";
import * as path from "path";
import { dirname } from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

async function generateTypes() {
  try {
    // Read local OpenAPI schema
    const schemaPath = path.resolve(__dirname, "../openapi.yaml");
    const schemaContent = fs.readFileSync(schemaPath, "utf-8");

    // Parse YAML schema
    const { load } = await import("js-yaml");
    const schema = load(schemaContent) as any;
    const apiUrl = "https://api.mulapin.com/v2";

    // Generate types
    const zodClient = await generateZodClientFromOpenAPI({
      openApiDoc: schema,
      options: {
        withImplicitRequiredProps: true,
        baseUrl: apiUrl,
      },
      disableWriteToFile: true,
    });

    // Create output directory
    const outputDir = path.resolve(__dirname, "../src/types/api");
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // Write only the generated client code
    fs.writeFileSync(`${outputDir}/client.ts`, zodClient);

    console.log("✅ API types generated successfully");
  } catch (error) {
    console.error("❌ Error generating types:", error);
  }
}

generateTypes();
