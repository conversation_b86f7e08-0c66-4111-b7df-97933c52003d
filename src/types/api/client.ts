import { make<PERSON><PERSON>, Zodios, type ZodiosOptions } from "@zodios/core";
import { z } from "zod";
import axios, { AxiosInstance, AxiosResponse, AxiosError } from "axios";

// Standard API response wrapper
const ApiResponse = <T extends z.ZodTypeAny>(dataSchema: T) =>
  z.object({
    data: dataSchema,
    message: z.string(),
    timestamp: z.string().datetime({ offset: true }),
    status: z.literal("success"),
  }).passthrough();

const postAuthlogin_Body = z
  .object({ email: z.string().email(), password: z.string() })
  .passthrough();
const UserProfile = z
  .object({
    user_id: z.string(),
    organization_id: z.string(),
    profile: z
      .object({
        email: z.string().email(),
        first_name: z.string(),
        last_name: z.string(),
        display_name: z.string(),
        avatar_url: z.string().url(),
      })
      .passthrough(),
    access_control: z
      .object({
        role: z.enum(["admin", "writer", "reader"]),
        department: z.string(),
        permissions: z.array(z.string()),
      })
      .passthrough(),
  })
  .passthrough();
const Error = z
  .object({
    error: z.string(),
    code: z.string(),
    message: z.string(),
    timestamp: z.string().datetime({ offset: true }),
  })
  .passthrough();
const Organization = z
  .object({
    organization_id: z.string(),
    name: z.string(),
    industry: z.string(),
    subscription_tier: z.enum(["starter", "professional", "enterprise"]),
    settings: z
      .object({
        timezone: z.string(),
        locale: z.string(),
        data_retention_days: z.number().int(),
      })
      .passthrough(),
    status: z.enum(["active", "suspended", "archived"]),
    created_at: z.string().datetime({ offset: true }),
    updated_at: z.string().datetime({ offset: true }),
  })
  .passthrough();
const OrganizationCreate = z
  .object({
    name: z.string(),
    industry: z.string(),
    subscription_tier: z
      .enum(["starter", "professional", "enterprise"])
      .optional()
      .default("starter"),
    settings: z
      .object({ timezone: z.string(), locale: z.string() })
      .passthrough()
      .optional(),
  })
  .passthrough();
const OrganizationDetails = Organization.and(
  z
    .object({
      business_structure: z
        .object({
          divisions: z.array(z.string()),
          channels: z.array(z.string()),
          products: z.array(z.string()),
          brands: z.array(z.string()),
        })
        .passthrough(),
      statistics: z
        .object({
          total_users: z.number().int(),
          total_maps: z.number().int(),
          total_personas: z.number().int(),
        })
        .passthrough(),
    })
    .passthrough()
);
const OrganizationUpdate = z
  .object({
    name: z.string().optional(),
    industry: z.string().optional(),
    settings: z
      .object({ 
        timezone: z.string().optional(), 
        currency: z.string().optional(),
        language: z.string().optional(),
        notifications_enabled: z.boolean().optional()
      })
      .passthrough().optional(),
    business_structure: z
      .object({ 
        divisions: z.array(z.string()).optional(), 
        channels: z.array(z.string()).optional(),
        products: z.array(z.string()).optional(),
        brands: z.array(z.string()).optional()
      })
      .passthrough().optional(),
  })
  .passthrough();
const UserReference = z
  .object({
    user_id: z.string(),
    name: z.string(),
    profile_image_url: z.string().url(),
    timestamp: z.string().datetime({ offset: true }),
  })
  .passthrough();
const User = z
  .object({
    user_id: z.string(),
    organization_id: z.string(),
    profile: z
      .object({
        email: z.string().email(),
        first_name: z.string(),
        last_name: z.string(),
        display_name: z.string(),
        avatar_url: z.string().url(),
      })
      .passthrough(),
    access_control: z
      .object({
        role: z.enum(["admin", "writer", "reader"]),
        department: z.string(),
        permissions: z.array(z.string()),
      })
      .passthrough(),
    created_by: UserReference,
    updated_by: UserReference,
  })
  .passthrough();
const UserCreate = z
  .object({
    email: z.string().email(),
    first_name: z.string(),
    last_name: z.string(),
    display_name: z.string().optional(),
    role: z.enum(["admin", "writer", "reader"]),
    department: z.string().optional(),
    permissions: z.array(z.string()).optional(),
  })
  .passthrough();
const UserDetails = User.and(
  z
    .object({
      recent_activity: z.array(
        z
          .object({
            action: z.string(),
            entity_type: z.string(),
            entity_id: z.string(),
            timestamp: z.string().datetime({ offset: true }),
          })
          .passthrough()
      ),
    })
    .passthrough()
);
const UserUpdate = z
  .object({
    profile: z
      .object({
        first_name: z.string(),
        last_name: z.string(),
        display_name: z.string(),
        avatar_url: z.string().url(),
      })
      .passthrough(),
    access_control: z
      .object({
        role: z.enum(["admin", "writer", "reader"]),
        department: z.string(),
        permissions: z.array(z.string()),
      })
      .passthrough(),
  })
  .passthrough();
const CustomerGoal = z
  .object({
    customer_goal_id: z.string(),
    organization_id: z.string(),
    name: z.string(),
    description: z.string(),
    category: z.string(),
    priority: z.enum(["high", "medium", "low"]),
    status: z.enum(["active", "draft", "archived"]),
    created_by: UserReference,
    updated_by: UserReference,
  })
  .passthrough();
const CustomerGoalCreate = z
  .object({
    name: z.string(),
    description: z.string(),
    category: z.string().optional(),
    priority: z.enum(["high", "medium", "low"]).optional().default("medium"),
  })
  .passthrough();
const CustomerGoalDetails = CustomerGoal.and(
  z
    .object({
      maps: z.array(
        z
          .object({ map_id: z.string(), name: z.string(), state: z.string() })
          .passthrough()
      ),
    })
    .passthrough()
);
const CustomerGoalUpdate = z
  .object({
    name: z.string().optional(),
    description: z.string().optional(),
    category: z.string().optional(),
    priority: z.enum(["high", "medium", "low"]).optional(),
    status: z.enum(["active", "draft", "archived"]).optional(),
  })
  .passthrough();
const Map = z
  .object({
    map_id: z.string(),
    organization_id: z.string(),
    customer_goal: CustomerGoal,
    name: z.string(),
    description: z.string(),
    division: z.string(),
    state: z.enum(["draft", "review", "published", "archived"]),
    status: z.enum(["active", "inactive"]),
    created_by: UserReference,
    updated_by: UserReference,
  })
  .passthrough();
const MapCreate = z
  .object({
    name: z.string(),
    description: z.string(),
    customer_goal_id: z.string(),
    division: z.string(),
    state: z.enum(["draft", "review", "published"]).optional().default("draft"),
  })
  .passthrough();
const Phase = z
  .object({
    phase_id: z.string(),
    organization_id: z.string(),
    map_id: z.string(),
    name: z.string(),
    description: z.string(),
    sequence: z.number().int(),
    components: z.array(z.string()),
    status: z.enum(["active", "inactive", "archived"]),
    created_by: UserReference,
    updated_by: UserReference,
  })
  .passthrough();
const Moment = z
  .object({
    moment_id: z.string(),
    organization_id: z.string(),
    phase_id: z.string(),
    name: z.string(),
    description: z.string(),
    image_url: z.string().url(),
    sequence: z.number().int().gte(1),
    components: z.array(z.string()),
    status: z.enum(["active", "inactive", "archived"]),
    created_by: UserReference,
    updated_by: UserReference,
  })
  .passthrough();
const Activity = z
  .object({
    activity_id: z.string(),
    organization_id: z.string(),
    moment_id: z.string(),
    name: z.string(),
    description: z.string(),
    activity_type: z.enum(["customer", "front_stage", "back_stage", "system"]),
    estimated_duration: z.string(),
    complexity_level: z.enum(["low", "medium", "high"]),
    personas: z.array(z.string()),
    sequence: z.number().int().gte(1),
    status: z.enum(["active", "inactive", "archived"]),
    created_by: UserReference,
    updated_by: UserReference,
  })
  .passthrough();
const Action = z
  .object({
    action_id: z.string(),
    organization_id: z.string(),
    activity_id: z.string(),
    name: z.string(),
    description: z.string(),
    sequence: z.number().int().gte(1),
    category: z.string(),
    link: z.string().url(),
    supported_by: z.string(),
    reference_screen: z.string(),
    delivery_epic: z.string(),
    additional_document_references: z.array(z.string()),
    priority: z.enum(["high", "medium", "low"]),
    estimated_effort: z.string(),
    dependencies: z.array(z.string()),
    usage_count: z.number().int(),
    last_used: z.string().datetime({ offset: true }),
    impact_score: z.number(),
    status: z.enum(["active", "inactive", "archived"]),
    created_by: UserReference,
    updated_by: UserReference,
  })
  .passthrough();
const ActivityDetails = Activity.and(
  z.object({ action_details: z.array(Action) }).passthrough()
);
const ActivitiesByType = z
  .object({
    customer: z.array(ActivityDetails),
    front_stage: z.array(ActivityDetails),
    back_stage: z.array(ActivityDetails),
    system: z.array(ActivityDetails),
  })
  .passthrough();
const MomentDetails = Moment.and(
  z.object({ activities: ActivitiesByType }).passthrough()
);
const Persona = z
  .object({
    persona_id: z.string(),
    organization_id: z.string(),
    name: z.string(),
    persona_type: z.enum(["customer", "front_stage", "back_stage", "system"]),
    image_url: z.string().url(),
    tagline: z.string(),
    age: z.number().int(),
    role: z.string(),
    income: z.string(),
    location: z.string(),
    status_description: z.string(),
    motivations: z.array(z.string()),
    frustrations: z.array(z.string()),
    goals: z.array(z.string()),
    preferences: z.array(z.string()),
    description: z.string(),
    category: z.string(),
    vendor: z.string(),
    platform: z.string(),
    availability: z.string(),
    notes: z.string(),
    usage_frequency: z.number().int(),
    status: z.enum([
      "active",
      "inactive",
      "maintenance",
      "deprecated",
      "archived",
    ]),
    created_by: UserReference,
    updated_by: UserReference,
  })
  .passthrough();
const PersonaDetails = Persona.and(
  z
    .object({
      moments_used: z.array(
        z
          .object({
            moment_id: z.string(),
            moment_name: z.string(),
            map_name: z.string(),
            activity_count: z.number().int(),
          })
          .passthrough()
      ),
    })
    .passthrough()
);
const Component = z
  .object({
    component_id: z.string(),
    organization_id: z.string(),
    numbering: z.string(),
    name: z.string(),
    description: z.string(),
    best_practices: z.string().optional(),
    level: z.string().optional(),
    framework: z.enum(["customer", "business"]).optional(),
    usage_count: z.number().int().optional(),
    last_used: z.string().datetime({ offset: true }).optional(),
    impact_score: z.number().optional(),
    efficiency_rating: z.number().optional(),
    status: z.enum(["active", "inactive", "archived"]),
    created_at: z.string().datetime({ offset: true }),
    updated_at: z.string().datetime({ offset: true }),
    created_by: z.string(),
    updated_by: z.string(),
  })
  .passthrough();
const ComponentDetails = Component.and(
  z
    .object({
      usage_details: z.array(
        z
          .object({
            entity_type: z.enum(["phase", "moment"]),
            entity_id: z.string(),
            entity_name: z.string(),
            map_name: z.string(),
          })
          .passthrough()
      ),
    })
    .passthrough()
);
const MapDetails = Map.and(
  z
    .object({
      phases: z.array(
        Phase.and(z.object({ moment_ids: z.array(z.string()) }).passthrough())
      ),
      moments: z.array(MomentDetails),
      personas: z.array(PersonaDetails),
      components: z.array(ComponentDetails),
      statistics: z
        .object({
          total_phases: z.number().int(),
          total_moments: z.number().int(),
          total_actions: z.number().int(),
          total_personas: z.number().int(),
          total_components: z.number().int(),
        })
        .passthrough(),
    })
    .passthrough()
);
const MapUpdate = z
  .object({
    name: z.string().optional(),
    description: z.string().optional(),
    division: z.string().optional(),
    state: z.enum(["draft", "review", "published", "archived"]).optional(),
    status: z.enum(["active", "inactive"]).optional(),
  })
  .passthrough();
const PhaseCreate = z
  .object({
    name: z.string(),
    description: z.string(),
    sequence: z.number().int().gte(1).optional(),
    components: z.array(z.string()).optional(),
  })
  .passthrough();
const PhaseDetails = Phase.and(
  z.object({ moment_ids: z.array(z.string()) }).passthrough()
);
const PhaseUpdate = z
  .object({
    name: z.string(),
    description: z.string(),
    sequence: z.number().int(),
    components: z.array(z.string()),
    status: z.enum(["active", "inactive", "archived"]),
  })
  .passthrough();
const postMomentsbatch_Body = z
  .object({ moment_ids: z.array(z.string()) })
  .passthrough();
const MomentCreate = z
  .object({
    name: z.string(),
    description: z.string(),
    image_url: z.string().url().optional(),
    sequence: z.number().int().gte(1).optional(),
    components: z.array(z.string()).optional(),
  })
  .passthrough();
const MomentUpdate = z
  .object({
    name: z.string(),
    description: z.string(),
    image_url: z.string().url(),
    sequence: z.number().int().gte(1),
    components: z.array(z.string()),
    status: z.enum(["active", "inactive", "archived"]),
  })
  .passthrough();
const ActivityCreate = z
  .object({
    name: z.string(),
    description: z.string(),
    activity_type: z.enum(["customer", "front_stage", "back_stage", "system"]),
    estimated_duration: z.string().optional(),
    complexity_level: z
      .enum(["low", "medium", "high"])
      .optional()
      .default("medium"),
    personas: z.array(z.string()).optional(),
    sequence: z.number().int().gte(1).optional(),
  })
  .passthrough();
const ActivityUpdate = z
  .object({
    name: z.string(),
    description: z.string(),
    activity_type: z.enum(["customer", "front_stage", "back_stage", "system"]),
    estimated_duration: z.string(),
    complexity_level: z.enum(["low", "medium", "high"]),
    personas: z.array(z.string()),
    sequence: z.number().int().gte(1),
    status: z.enum(["active", "inactive", "archived"]),
  })
  .passthrough();
const ActionCreate = z
  .object({
    name: z.string(),
    description: z.string(),
    sequence: z.number().int().gte(1),
    category: z.string(),
    link: z.string().url().optional(),
    supported_by: z.string().optional(),
    reference_screen: z.string().optional(),
    delivery_epic: z.string().optional(),
    additional_document_references: z.array(z.string()).optional(),
    priority: z.enum(["high", "medium", "low"]).optional().default("medium"),
    estimated_effort: z.string().optional(),
    dependencies: z.array(z.string()).optional(),
  })
  .passthrough();
const ActionDetails = Action;
const ActionUpdate = z
  .object({
    name: z.string(),
    description: z.string(),
    sequence: z.number().int().gte(1),
    category: z.string(),
    link: z.string().url(),
    supported_by: z.string(),
    reference_screen: z.string(),
    delivery_epic: z.string(),
    additional_document_references: z.array(z.string()),
    priority: z.enum(["high", "medium", "low"]),
    estimated_effort: z.string(),
    dependencies: z.array(z.string()),
    status: z.enum(["active", "inactive", "archived"]),
  })
  .passthrough();
const PersonaCreate = z
  .object({
    name: z.string(),
    persona_type: z.enum(["customer", "front_stage", "back_stage", "system"]),
    image_url: z.string().url().optional(),
    tagline: z.string().optional(),
    age: z.number().int().gte(1).lte(120).optional(),
    role: z.string().optional(),
    income: z.string().optional(),
    location: z.string().optional(),
    status_description: z.string().optional(),
    motivations: z.array(z.string()).optional(),
    frustrations: z.array(z.string()).optional(),
    goals: z.array(z.string()).optional(),
    preferences: z.array(z.string()).optional(),
    description: z.string().optional(),
    category: z.string().optional(),
    vendor: z.string().optional(),
    platform: z.string().optional(),
    availability: z.string().optional(),
    notes: z.string().optional(),
  })
  .passthrough();
const PersonaUpdate = z
  .object({
    name: z.string(),
    persona_type: z.enum(["customer", "front_stage", "back_stage", "system"]),
    image_url: z.string().url(),
    tagline: z.string(),
    age: z.number().int(),
    role: z.string(),
    income: z.string(),
    location: z.string(),
    status_description: z.string(),
    motivations: z.array(z.string()),
    frustrations: z.array(z.string()),
    goals: z.array(z.string()),
    preferences: z.array(z.string()),
    description: z.string(),
    category: z.string(),
    vendor: z.string(),
    platform: z.string(),
    availability: z.string(),
    notes: z.string(),
    status: z.enum([
      "active",
      "inactive",
      "maintenance",
      "deprecated",
      "archived",
    ]),
  })
  .passthrough();
const ComponentCreate = z
  .object({
    numbering: z.string(),
    name: z.string(),
    description: z.string(),
    best_practices: z.string().optional(),
    count: z.number().int().optional(),
    level: z.string().optional(),
    framework: z.enum(["customer", "business"]).optional(),
  })
  .passthrough();
const ComponentUpdate = z
  .object({
    numbering: z.string().optional(),
    name: z.string().optional(),
    description: z.string().optional(),
    best_practices: z.string().optional(),
    level: z.string().optional(),
    framework: z.enum(["customer", "business"]).optional(),
    status: z.enum(["active", "inactive", "archived"]).optional(),
  })
  .passthrough();
const postComponentsmatch_Body = z
  .object({
    text: z.string(),
    threshold: z.number().gte(0).lte(1).optional().default(0.7),
  })
  .passthrough();

const ComponentUploadResult = z
  .object({
    total_rows_processed: z.number().int(),
    components_created: z.number().int(),
    components_skipped: z.number().int(),
    validation_errors: z.array(z.any()),
    created_component_ids: z.array(z.string()),
    processing_time_ms: z.number().int(),
  })
  .passthrough();

const postComponentsupload_Body = z
  .object({
    csv_file_location: z.string().url(),
    overwrite_existing: z.boolean().optional().default(false),
    validate_only: z.boolean().optional().default(false),
  })
  .passthrough();
const ComponentMatch = z
  .object({
    component_id: z.string(),
    component_name: z.string(),
    numbering: z.string(),
    similarity_score: z.number().gte(0).lte(1),
    description: z.string(),
  })
  .passthrough();
const postAigenerateText_Body = z
  .object({
    content_type: z.enum([
      "customer_goal_description",
      "map_description",
      "phase_description",
      "moment_description",
      "activity_description",
      "action_description",
      "persona_description",
      "persona_goals",
      "persona_frustrations",
    ]),
    context: z.object({}).passthrough(),
    tone: z
      .enum(["professional", "casual", "technical", "friendly"])
      .optional()
      .default("professional"),
    length: z.enum(["short", "medium", "long"]).optional().default("medium"),
  })
  .passthrough();
const postAigenerateImage_Body = z
  .object({
    image_type: z.enum(["persona_avatar", "map_illustration", "phase_icon"]),
    parameters: z.object({}).passthrough(),
    style: z
      .enum(["professional", "modern", "minimalist", "colorful"])
      .optional()
      .default("professional"),
  })
  .passthrough();

export const schemas = {
  ApiResponse,
  postAuthlogin_Body,
  UserProfile,
  Error,
  Organization,
  OrganizationCreate,
  OrganizationDetails,
  OrganizationUpdate,
  UserReference,
  User,
  UserCreate,
  UserDetails,
  UserUpdate,
  CustomerGoal,
  CustomerGoalCreate,
  CustomerGoalDetails,
  CustomerGoalUpdate,
  Map,
  MapCreate,
  Phase,
  Moment,
  Activity,
  Action,
  ActivityDetails,
  ActivitiesByType,
  MomentDetails,
  Persona,
  PersonaDetails,
  Component,
  ComponentDetails,
  MapDetails,
  MapUpdate,
  PhaseCreate,
  PhaseDetails,
  PhaseUpdate,
  postMomentsbatch_Body,
  MomentCreate,
  MomentUpdate,
  ActivityCreate,
  ActivityUpdate,
  ActionCreate,
  ActionDetails,
  ActionUpdate,
  PersonaCreate,
  PersonaUpdate,
  ComponentCreate,
  ComponentUpdate,
  postComponentsmatch_Body,
  ComponentMatch,
  ComponentUploadResult,
  postComponentsupload_Body,
  postAigenerateText_Body,
  postAigenerateImage_Body,
};

const endpoints = makeApi([
  {
    method: "post",
    path: "/organization",
    description: "Create Organization (User Onboarding)",
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: OrganizationCreate,
      },
    ],
    response: ApiResponse(z.object({
      organization: OrganizationDetails,
      user: User,
    })),
    errors: [
      {
        status: 409,
        description: "User already has an organization",
        schema: z.object({
          error: z.string(),
          existing_organization_id: z.string(),
        }),
      },
    ],
  },
  {
    method: "get",
    path: "/actions/:actionId",
    requestFormat: "json",
    parameters: [
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
      {
        name: "actionId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: ActionDetails,
  },
  {
    method: "put",
    path: "/actions/:actionId",
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: ActionUpdate,
      },
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
      {
        name: "actionId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: Action,
  },
  {
    method: "delete",
    path: "/actions/:actionId",
    requestFormat: "json",
    parameters: [
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
      {
        name: "actionId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: z.void(),
  },
  {
    method: "get",
    path: "/activities/:activityId",
    requestFormat: "json",
    parameters: [
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
      {
        name: "activityId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: ActivityDetails,
  },
  {
    method: "put",
    path: "/activities/:activityId",
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: ActivityUpdate,
      },
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
      {
        name: "activityId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: Activity,
  },
  {
    method: "delete",
    path: "/activities/:activityId",
    requestFormat: "json",
    parameters: [
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
      {
        name: "activityId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: z.void(),
  },
  {
    method: "get",
    path: "/activities/:activityId/actions",
    description: `Retrieve all actions within a specific activity`,
    requestFormat: "json",
    parameters: [
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
      {
        name: "activityId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: z.array(Action),
  },
  {
    method: "post",
    path: "/activities/:activityId/actions",
    description: `Create a new action within a specific activity`,
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: ActionCreate,
      },
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
      {
        name: "activityId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: Action,
  },
  {
    method: "get",
    path: "/admin/organizations",
    description: `Super admin only - retrieve all organizations`,
    requestFormat: "json",
    response: z.array(Organization),
  },
  {
    method: "post",
    path: "/admin/organizations",
    description: `Super admin only - create new organization`,
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: OrganizationCreate,
      },
    ],
    response: Organization,
  },
  {
    method: "get",
    path: "/admin/organizations/:orgId",
    requestFormat: "json",
    parameters: [
      {
        name: "orgId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: OrganizationDetails,
  },
  {
    method: "put",
    path: "/admin/organizations/:orgId",
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: OrganizationUpdate,
      },
      {
        name: "orgId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: Organization,
  },
  {
    method: "post",
    path: "/ai/generate-image",
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: postAigenerateImage_Body,
      },
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
    ],
    response: z
      .object({ image_url: z.string().url(), thumbnail_url: z.string().url() })
      .passthrough(),
  },
  {
    method: "post",
    path: "/ai/generate-text",
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: postAigenerateText_Body,
      },
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
    ],
    response: z
      .object({
        generated_text: z.string(),
        metadata: z
          .object({ model_used: z.string(), token_count: z.number().int() })
          .passthrough(),
      })
      .passthrough(),
  },
  {
    method: "post",
    path: "/auth/login",
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: postAuthlogin_Body,
      },
    ],
    response: z
      .object({
        access_token: z.string(),
        expires_in: z.number().int(),
        user: UserProfile,
      })
      .passthrough(),
    errors: [
      {
        status: 401,
        description: `Authentication required`,
        schema: Error,
      },
    ],
  },
  {
    method: "get",
    path: "/components",
    requestFormat: "json",
    parameters: [
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
    ],
    response: z.array(Component),
  },
  {
    method: "post",
    path: "/components",
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: ComponentCreate,
      },
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
    ],
    response: Component,
  },
  {
    method: "get",
    path: "/components/:componentId",
    requestFormat: "json",
    parameters: [
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
      {
        name: "componentId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: ComponentDetails,
  },
  {
    method: "put",
    path: "/components/:componentId",
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: ComponentUpdate,
      },
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
      {
        name: "componentId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: Component,
  },
  {
    method: "delete",
    path: "/components/:componentId",
    requestFormat: "json",
    parameters: [
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
      {
        name: "componentId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: z.void(),
  },
  {
    method: "post",
    path: "/components/match",
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: postComponentsmatch_Body,
      },
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
    ],
    response: z.array(ComponentMatch),
  },
  {
    method: "get",
    path: "/customer-goals",
    requestFormat: "json",
    parameters: [
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
    ],
    response: z.array(CustomerGoal),
  },
  {
    method: "post",
    path: "/customer-goals",
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: CustomerGoalCreate,
      },
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
    ],
    response: CustomerGoal,
  },
  {
    method: "get",
    path: "/customer-goals/:customer_goal_id",
    requestFormat: "json",
    parameters: [
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
      {
        name: "customer_goal_id",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: CustomerGoalDetails,
  },
  {
    method: "put",
    path: "/customer-goals/:customer_goal_id",
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: CustomerGoalUpdate,
      },
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
      {
        name: "customer_goal_id",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: CustomerGoal,
  },
  {
    method: "delete",
    path: "/customer-goals/:customer_goal_id",
    requestFormat: "json",
    parameters: [
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
      {
        name: "customer_goal_id",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: z.void(),
  },
  {
    method: "get",
    path: "/maps",
    requestFormat: "json",
    parameters: [
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
    ],
    response: z.array(Map),
  },
  {
    method: "post",
    path: "/maps",
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: MapCreate,
      },
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
    ],
    response: Map,
  },
  {
    method: "get",
    path: "/maps/:mapId",
    requestFormat: "json",
    parameters: [
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
      {
        name: "mapId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: MapDetails,
  },
  {
    method: "put",
    path: "/maps/:mapId",
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: MapUpdate,
      },
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
      {
        name: "mapId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: Map,
  },
  {
    method: "delete",
    path: "/maps/:mapId",
    requestFormat: "json",
    parameters: [
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
      {
        name: "mapId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: z.void(),
  },
  {
    method: "get",
    path: "/maps/:mapId/phases",
    requestFormat: "json",
    parameters: [
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
      {
        name: "mapId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: z.array(Phase),
  },
  {
    method: "post",
    path: "/maps/:mapId/phases",
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: PhaseCreate,
      },
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
      {
        name: "mapId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: Phase,
  },
  {
    method: "get",
    path: "/maps/:mapId/phases/:phaseId/moments",
    requestFormat: "json",
    parameters: [
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
      {
        name: "mapId",
        type: "Path",
        schema: z.string(),
      },
      {
        name: "phaseId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: z.array(Moment),
  },
  {
    method: "post",
    path: "/maps/:mapId/phases/:phaseId/moments",
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: MomentCreate,
      },
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
      {
        name: "mapId",
        type: "Path",
        schema: z.string(),
      },
      {
        name: "phaseId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: Moment,
  },
  {
    method: "get",
    path: "/moments",
    requestFormat: "json",
    parameters: [
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
    ],
    response: z.array(Moment),
  },
  {
    method: "get",
    path: "/moments/:momentId",
    requestFormat: "json",
    parameters: [
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
      {
        name: "momentId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: MomentDetails,
  },
  {
    method: "put",
    path: "/moments/:momentId",
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: MomentUpdate,
      },
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
      {
        name: "momentId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: Moment,
  },
  {
    method: "delete",
    path: "/moments/:momentId",
    requestFormat: "json",
    parameters: [
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
      {
        name: "momentId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: z.void(),
  },
  {
    method: "get",
    path: "/moments/:momentId/activities",
    description: `Retrieve all activities for a moment, grouped by activity type`,
    requestFormat: "json",
    parameters: [
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
      {
        name: "momentId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: ActivitiesByType,
  },
  {
    method: "post",
    path: "/moments/:momentId/activities",
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: ActivityCreate,
      },
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
      {
        name: "momentId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: Activity,
  },
  {
    method: "post",
    path: "/moments/batch",
    description: `Retrieve multiple moments by providing an array of moment IDs in the request body`,
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: postMomentsbatch_Body,
      },
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
    ],
    response: z.array(MomentDetails),
  },
  {
    method: "get",
    path: "/organization",
    requestFormat: "json",
    parameters: [
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
    ],
    response: OrganizationDetails,
  },
  {
    method: "put",
    path: "/organization",
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: OrganizationUpdate,
      },
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
    ],
    response: Organization,
  },
  {
    method: "get",
    path: "/personas",
    requestFormat: "json",
    parameters: [
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
    ],
    response: z.array(Persona),
  },
  {
    method: "post",
    path: "/personas",
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: PersonaCreate,
      },
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
    ],
    response: Persona,
  },
  {
    method: "get",
    path: "/personas/:personaId",
    requestFormat: "json",
    parameters: [
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
      {
        name: "personaId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: PersonaDetails,
  },
  {
    method: "put",
    path: "/personas/:personaId",
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: PersonaUpdate,
      },
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
      {
        name: "personaId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: Persona,
  },
  {
    method: "delete",
    path: "/personas/:personaId",
    requestFormat: "json",
    parameters: [
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
      {
        name: "personaId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: z.void(),
  },
  {
    method: "get",
    path: "/personas/type/:personaType",
    requestFormat: "json",
    parameters: [
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
      {
        name: "personaType",
        type: "Path",
        schema: z.enum(["customer", "front_stage", "back_stage", "system"]),
      },
    ],
    response: z.array(Persona),
  },
  {
    method: "get",
    path: "/phases/:phaseId",
    requestFormat: "json",
    parameters: [
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
      {
        name: "phaseId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: PhaseDetails,
  },
  {
    method: "put",
    path: "/phases/:phaseId",
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: PhaseUpdate,
      },
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
      {
        name: "phaseId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: Phase,
  },
  {
    method: "delete",
    path: "/phases/:phaseId",
    requestFormat: "json",
    parameters: [
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
      {
        name: "phaseId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: z.void(),
  },
  {
    method: "get",
    path: "/users",
    requestFormat: "json",
    parameters: [
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
    ],
    response: z.array(User),
  },
  {
    method: "post",
    path: "/users",
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: UserCreate,
      },
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
    ],
    response: User,
  },
  {
    method: "get",
    path: "/users/:userId",
    requestFormat: "json",
    parameters: [
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
      {
        name: "userId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: UserDetails,
  },
  {
    method: "put",
    path: "/users/:userId",
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: UserUpdate,
      },
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
      {
        name: "userId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: User,
  },
  {
    method: "delete",
    path: "/users/:userId",
    requestFormat: "json",
    parameters: [
      {
        name: "X-Organization-Id",
        type: "Header",
        schema: z.string().optional(),
      },
      {
        name: "userId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: z.void(),
  },
  {
    method: "post",
    path: "/components/upload",
    description: "Bulk Upload Components from CSV",
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: postComponentsupload_Body,
      },
    ],
    response: ApiResponse(ComponentUploadResult),
  },
]);

export const api = new Zodios("https://api.mulapin.com/v2", endpoints);

export function createApiClient(baseUrl: string, options?: ZodiosOptions) {
  return new Zodios(baseUrl, endpoints, options);
}

// Export TypeScript types from Zod schemas
export type LoginRequest = z.infer<typeof postAuthlogin_Body>;
export type UserProfile = z.infer<typeof UserProfile>;
export type ApiError = z.infer<typeof Error>;
export type Organization = z.infer<typeof Organization>;
export type OrganizationCreate = z.infer<typeof OrganizationCreate>;
export type OrganizationUpdate = z.infer<typeof OrganizationUpdate>;
export type OrganizationDetails = z.infer<typeof OrganizationDetails>;
export type UserReference = z.infer<typeof UserReference>;
export type User = z.infer<typeof User>;
export type UserCreate = z.infer<typeof UserCreate>;
export type UserUpdate = z.infer<typeof UserUpdate>;
export type UserDetails = z.infer<typeof UserDetails>;

// Customer Goals
export type CustomerGoal = z.infer<typeof CustomerGoal>;
export type CustomerGoalCreate = z.infer<typeof CustomerGoalCreate>;
export type CustomerGoalUpdate = z.infer<typeof CustomerGoalUpdate>;
export type CustomerGoalDetails = z.infer<typeof CustomerGoalDetails>;

// Maps
export type Map = z.infer<typeof Map>;
export type MapCreate = z.infer<typeof MapCreate>;
export type MapUpdate = z.infer<typeof MapUpdate>;
export type MapDetails = z.infer<typeof MapDetails>;

// Phases
export type Phase = z.infer<typeof Phase>;
export type PhaseCreate = z.infer<typeof PhaseCreate>;
export type PhaseUpdate = z.infer<typeof PhaseUpdate>;
export type PhaseDetails = z.infer<typeof PhaseDetails>;

// Moments
export type Moment = z.infer<typeof Moment>;
export type MomentCreate = z.infer<typeof MomentCreate>;
export type MomentUpdate = z.infer<typeof MomentUpdate>;
export type MomentDetails = z.infer<typeof MomentDetails>;

// Activities
export type Activity = z.infer<typeof Activity>;
export type ActivityCreate = z.infer<typeof ActivityCreate>;
export type ActivityUpdate = z.infer<typeof ActivityUpdate>;
export type ActivityDetails = z.infer<typeof ActivityDetails>;
export type ActivitiesByType = z.infer<typeof ActivitiesByType>;

// Actions
export type Action = z.infer<typeof Action>;
export type ActionCreate = z.infer<typeof ActionCreate>;
export type ActionUpdate = z.infer<typeof ActionUpdate>;
export type ActionDetails = z.infer<typeof ActionDetails>;

// Personas
export type Persona = z.infer<typeof Persona>;
export type PersonaCreate = z.infer<typeof PersonaCreate>;
export type PersonaUpdate = z.infer<typeof PersonaUpdate>;
export type PersonaDetails = z.infer<typeof PersonaDetails>;

// Components
export type Component = z.infer<typeof Component>;
export type ComponentCreate = z.infer<typeof ComponentCreate>;
export type ComponentUpdate = z.infer<typeof ComponentUpdate>;
export type ComponentDetails = z.infer<typeof ComponentDetails>;
export type ComponentMatch = z.infer<typeof ComponentMatch>;
export type ComponentMatchRequest = z.infer<typeof postComponentsmatch_Body>;
export type ComponentUploadResult = z.infer<typeof ComponentUploadResult>;
export type ComponentUploadRequest = z.infer<typeof postComponentsupload_Body>;

// Custom API Client with enhanced error handling and authentication
class ApiV2Client {
  private api: AxiosInstance;
  private baseURL: string;

  constructor() {
    this.baseURL = process.env.NEXT_PUBLIC_API_V2_BASE_URL || "https://z0p4210n29.execute-api.ap-southeast-2.amazonaws.com/Prod";
    
    this.api = axios.create({
      baseURL: this.baseURL,
      headers: {
        "Content-Type": "application/json",
      },
    });

    // Only initialize on client-side
    if (typeof window !== "undefined") {
      this.initializeAuthToken();
      this.setupInterceptors();
    }
  }

  private initializeAuthToken() {
    if (typeof window !== "undefined") {
      const token = localStorage.getItem("authToken");
      if (token) {
        this.setAuthToken(token);
      }
    }
  }

  private setupInterceptors() {
    // Request interceptor
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem("authToken");
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    this.api.interceptors.response.use(
      (response) => response,
      (error: AxiosError<ApiError>) => {
        // Handle common API errors
        if (error.response?.status === 401) {
          // Unauthorized - clear token and redirect to login
          this.clearAuthToken();
          window.location.href = '/login';
        }
        
        // Transform error for consistent handling
        const apiError: ApiError = {
          error: error.response?.data?.error || 'unknown_error',
          code: error.response?.data?.code || 'HTTP_' + error.response?.status,
          message: error.response?.data?.message || error.message,
          timestamp: error.response?.data?.timestamp || new Date().toISOString(),
        };
        
        return Promise.reject(apiError);
      }
    );
  }

  // Helper to set the Bearer token
  setAuthToken(token: string) {
    this.api.defaults.headers.common["Authorization"] = `Bearer ${token}`;
    if (typeof window !== "undefined") {
      localStorage.setItem("authToken", token);
    }
  }

  // Clear the Bearer token
  clearAuthToken() {
    delete this.api.defaults.headers.common["Authorization"];
    if (typeof window !== "undefined") {
      localStorage.removeItem("authToken");
      localStorage.removeItem("username");
    }
  }

  // Set organization override header (for super admin users)
  setOrganizationOverride(orgId?: string) {
    if (orgId) {
      this.api.defaults.headers.common["X-Organization-Id"] = orgId;
    } else {
      delete this.api.defaults.headers.common["X-Organization-Id"];
    }
  }

  // Basic GET request
  async get<T>(url: string, params?: Record<string, any>): Promise<AxiosResponse<T>> {
    return this.api.get<T>(url, { params });
  }

  // Basic POST request
  async post<T>(url: string, data?: any): Promise<AxiosResponse<T>> {
    return this.api.post<T>(url, data);
  }

  // Basic PUT request
  async put<T>(url: string, data?: any): Promise<AxiosResponse<T>> {
    return this.api.put<T>(url, data);
  }

  // Basic PATCH request
  async patch<T>(url: string, data?: any): Promise<AxiosResponse<T>> {
    return this.api.patch<T>(url, data);
  }

  // Basic DELETE request
  async delete<T>(url: string): Promise<AxiosResponse<T>> {
    return this.api.delete<T>(url);
  }

  // Form data POST (for file uploads)
  async postFormData<T>(url: string, formData: FormData): Promise<AxiosResponse<T>> {
    return this.api.post<T>(url, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  }
}

// Export singleton instance
export const apiV2Client = new ApiV2Client();
export default apiV2Client;
