// API compatible types - aligning with existing mapCanvas data structures
export interface Phase {
  id?: number | string;
  name?: string;
  order?: number;
  created_at?: string;
  updated_at?: string;
  desc?: string;
  image?: string | null;
  components?: any[];
  component_ids?: string[]; // Updated to match API v2 schema
}

// Moment type for new API v2 structure
export interface Moment {
  id?: number | string;
  name?: string;
  order?: number;
  created_at?: string;
  updated_at?: string;
  desc?: string;
  image?: string | null;
  components?: any[];
  component_ids?: string[]; // Updated to match API v2 schema
}

export interface Action {
  id?: string | number;
  name?: string;
  desc?: string;
  phase?: number;
  block?: number | null;
  role?: any;
  is_active?: boolean;
  is_draft?: boolean;
  parent?: string | null;
  knowledge?: string | null;
  channel?: any[];
  type?: string;
  order?: number;
  created_at?: string;
  updated_at?: string;
  map_role_id?: number;
}

// Activity type for new API v2 structure
export interface Activity {
  id?: string | number;
  name?: string;
  desc?: string;
  moment_id?: string;
  activity_type?: "customer" | "front_stage" | "back_stage" | "system";
  personas?: string[];
  sequence?: number;
  created_at?: string;
  updated_at?: string;
}

export interface BuildingBlock {
  connection_id?: number;
  connection_type?: number;
  phase?: number;
  id?: number;
  type?: number | any;
  sub_type?: number | null | any;
  role?: number;
  brand?: number | null;
  segment?: number | null;
  name?: string;
  desc?: string;
  order?: number;
  vote?: number;
  content?: any | null;
  created_at?: string;
  updated_at?: string;
  typeObject?: any;
  subTypeObject?: any;
}

export interface MapRole {
  id: number;
  name: string;
  image: string | null;
  desc: string;
  order: number;
  map_role_id: number;
  type: string;
}

export interface RowGroup {
  id: string;
  name: string;
  type: "story" | "action" | "activity" | "block" | "system";
  expandable: boolean;
  subRows: SubRow[];
  component?: React.ComponentType<CellProps>;
}

export interface SubRow {
  id: string;
  name: string;
  parentId: string;
  type: string;
}

export interface VisibleRow extends Partial<SubRow> {
  id: string;
  name: string;
  type: string;
  parentId?: string;
  isFirstSubRow?: boolean;
  isLastSubRow?: boolean;
  parentSpan?: number;
  subRows?: SubRow[];
}

export interface CellProps {
  row: VisibleRow;
  phase: Phase;
  data?: Action | BuildingBlock;
  actions?: Action[];
  buildingBlocks?: BuildingBlock[];
  roles?: MapRole[];
}

export interface GridPosition {
  x: number;
  y: number;
}

export interface GridState {
  scale: number;
  scrollPos: GridPosition;
  mode: "pan" | "select";
  expandedRows: Record<string, boolean>;
  hiddenColumns: string[];
  hiddenRows: string[];
  selectedCells: Set<string>;
  isStoryMode: boolean;
}
