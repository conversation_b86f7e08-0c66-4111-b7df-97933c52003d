import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { apiV2Client } from '../../../types/api/client';
import type { 
  LoginRequest, 
  UserProfile 
} from '../../../types/api/client';
import { mockUser } from '../mocks/mockData';

// Login response interface (not in OpenAPI schema since it's the response wrapper)
export interface LoginResponse {
  access_token: string;
  expires_in: number;
  user: UserProfile;
}

// Query keys
export const authKeys = {
  all: ['auth'] as const,
  user: () => [...authKeys.all, 'user'] as const,
};

// Mock data toggle
const USE_MOCK_DATA = process.env.NODE_ENV === 'development' && process.env.NEXT_PUBLIC_USE_MOCK_API === 'true';

// Login mutation
export function useLogin() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (credentials: LoginRequest): Promise<LoginResponse> => {
      if (USE_MOCK_DATA) {
        // Simulate API delay and return mock login response
        await new Promise(resolve => setTimeout(resolve, 1000));
        return {
          access_token: 'mock_jwt_token_' + Date.now(),
          expires_in: 3600,
          user: mockUser,
        };
      }
      const response = await apiV2Client.post<LoginResponse>('/auth/login', credentials);
      return response.data;
    },
    onSuccess: (data) => {
      // Set the auth token
      apiV2Client.setAuthToken(data.access_token);
      
      // Cache user data
      queryClient.setQueryData(authKeys.user(), data.user);
      
      // Store user info in localStorage for persistence
      localStorage.setItem('user', JSON.stringify(data.user));
    },
    onError: () => {
      // Clear any existing auth data on login failure
      apiV2Client.clearAuthToken();
      localStorage.removeItem('user');
    },
  });
}

// Logout mutation
export function useLogout() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (): Promise<void> => {
      // Clear auth token
      apiV2Client.clearAuthToken();
    },
    onSuccess: () => {
      // Clear all cached data
      queryClient.clear();
      
      // Clear localStorage
      localStorage.removeItem('user');
      
      // Redirect to login
      if (typeof window !== 'undefined') {
        window.location.href = '/login';
      }
    },
  });
}

// Get current user query
export function useCurrentUser() {
  return useQuery({
    queryKey: authKeys.user(),
    queryFn: async (): Promise<UserProfile | null> => {
      try {
        // First try to get from localStorage
        if (typeof window !== 'undefined') {
          const stored = localStorage.getItem('user');
          if (stored) {
            return JSON.parse(stored);
          }
        }
        return null;
      } catch {
        return null;
      }
    },
    staleTime: Infinity, // User data doesn't change often
  });
}

// Check if user is authenticated
export function useIsAuthenticated(): boolean {
  const { data: user } = useCurrentUser();
  
  if (typeof window === 'undefined') return false;
  
  const token = localStorage.getItem('authToken');
  return !!(token && user);
}

// Get user permissions
export function useUserPermissions(): string[] {
  const { data: user } = useCurrentUser();
  return user?.access_control?.permissions || [];
}

// Check if user has specific permission
export function useHasPermission(permission: string): boolean {
  const permissions = useUserPermissions();
  return permissions.includes(permission);
}

// Check user role
export function useUserRole(): 'admin' | 'writer' | 'reader' | null {
  const { data: user } = useCurrentUser();
  return user?.access_control?.role || null;
}