import { Phase } from "@/types/map";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import CheckIcon from "@mui/icons-material/Check";
import CloseIcon from "@mui/icons-material/Close";
import DeleteIcon from "@mui/icons-material/Delete";
import DragIndicatorIcon from "@mui/icons-material/DragIndicator";
import EditIcon from "@mui/icons-material/Edit";
import { Box, Card, IconButton, Stack, TextField, Tooltip, Typography } from "@mui/material";
import React, { useState } from "react";
import MapActionCell from "./MapActionCell";
import MapBuildingBlockCell from "./MapBuildingBlockCell";
import StoryCard from "./StoryCard";

type MapPhaseColumnProps = {
  phase: Phase;
  actionsByTypeRole: {
    [key: string]: {
      phases: { [phaseId: number]: any[] };
      minHeight: number;
    };
  };
  buildingBlocksByType: {
    [key: string]: {
      phases: { [phaseId: number]: any[] };
      minHeight: number;
      typeName: string;
      type: number;
    };
  };
  onDeletePhase: (id: number) => void;
  onEditPhase: (phase: Phase) => void;
  onEditAction: (id: string, updatedText: string, cellType: string) => void;
  onDeleteAction: (id: string, cellType: string) => void;
  onAddAction: (id: number, type: string | number, role?: number) => void;
  onViewExistingData?: (insights: any[], phaseId: number, typeId: number) => void;
  onBuildingBlockCreated?: (buildingBlock: any) => void;
  isPanningDisabled: boolean;
  isCompactView: boolean;
  isEmpty?: boolean;
  mapId?: string;
  transformValues?: {
    scale: number;
    positionX: number;
    positionY: number;
  };
};

const MapPhaseColumn: React.FC<MapPhaseColumnProps> = ({
  phase,
  actionsByTypeRole,
  buildingBlocksByType,
  onDeletePhase,
  onEditPhase,
  onEditAction,
  onDeleteAction,
  onAddAction,
  onViewExistingData,
  onBuildingBlockCreated,
  isPanningDisabled,
  isCompactView,
  isEmpty = false,
  mapId,
  transformValues,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [label, setLabel] = useState(phase.name);
  const [tempLabel, setTempLabel] = useState(phase.name);
  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({
    id: `phase-${phase.id}`,
    disabled: !isPanningDisabled,
  });

  const handleEditClick = () => setIsEditing(true);
  const handleCancelClick = () => {
    setTempLabel(label);
    setIsEditing(false);
  };
  const handleSaveClick = () => {
    setLabel(tempLabel);
    onEditPhase({ id: phase.id, name: tempLabel });
    setIsEditing(false);
  };

  return (
    <Box
      sx={{
        opacity: isEmpty ? 0.5 : 1,
        transform: CSS.Transform.toString(transform),
        transition,
        marginLeft: isCompactView ? "1rem !important" : "0px",
        marginBottom: isCompactView ? "1rem !important" : "0px",
      }}
    >
      <Stack
        ref={setNodeRef}
        sx={{
          minWidth: 300,
          ml: "1rem",
          position: "relative",
          cursor: "default", // Override default drag cursor
        }}
      >
        <Card sx={{ backgroundColor: "map.header", padding: 2, position: "relative" }}>
          {isPanningDisabled && !isCompactView && (
            <IconButton
              {...attributes}
              {...listeners}
              sx={{
                position: "absolute",
                right: 8,
                top: "50%",
                transform: "translateY(-50%)",
                cursor: "grab",
                "&:active": {
                  cursor: "grabbing",
                },
                zIndex: 1,
              }}
            >
              <DragIndicatorIcon />
            </IconButton>
          )}
          {isEditing ? (
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <TextField
                value={tempLabel}
                onChange={(e) => setTempLabel(e.target.value)}
                variant="outlined"
                size="small"
                autoFocus
                sx={{ width: "70%" }}
              />
              <IconButton onClick={handleSaveClick} sx={{ ml: 1 }}>
                <CheckIcon />
              </IconButton>
              <IconButton onClick={handleCancelClick} sx={{ ml: 1 }}>
                <CloseIcon />
              </IconButton>
            </Box>
          ) : (
            <Tooltip
              title={
                <Box>
                  <IconButton onClick={handleEditClick}>
                    <EditIcon fontSize="small" />
                  </IconButton>
                  <IconButton onClick={() => onDeletePhase(phase.id)}>
                    <DeleteIcon fontSize="small" />
                  </IconButton>
                </Box>
              }
              arrow
              placement="top"
            >
              <Typography variant="h6" align="center">
                {label}
              </Typography>
            </Tooltip>
          )}
        </Card>

        <Stack direction="column" spacing={2} sx={{ marginTop: 2 }}>
          <StoryCard
            image={phase.image}
            name={phase.name}
            desc={phase.desc}
            id={phase.id}
            components={phase.components}
            onEdit={onEditPhase}
          />
          {!isCompactView && (
            <>
              {/* Action Cells */}
              {Object.entries(actionsByTypeRole).map(
                ([typeRoleKey, { phases, minHeight: maxHeight }]) => {
                  const actions = phases[phase.id] || [];

                  return (
                    <MapActionCell
                      key={`${typeRoleKey}-${phase.id}`}
                      actions={actions}
                      height={maxHeight}
                      onEditAction={(id, newText) => onEditAction(id, newText, "action")}
                      onDeleteAction={(id) => onDeleteAction(id, "action")}
                      onAddAction={() => onAddAction(phase.id, typeRoleKey)}
                      phaseId={phase.id}
                      type={typeRoleKey}
                      role={typeRoleKey}
                      isPanningDisabled={isPanningDisabled}
                    />
                  );
                }
              )}

              {/* Building Block Cells */}
              {Object.entries(buildingBlocksByType)
                .sort(([, entryA], [, entryB]) => {
                  // Define the order based on the toggle menu
                  const orderMap: Record<string, number> = {
                    Painpoints: 1,
                    Opportunity: 2,
                    "Voice of customer": 3,
                    "Operation context": 4,
                    Analytics: 5,
                    // Add any other types with higher numbers
                  };

                  // Get the order for each entry
                  const orderA = orderMap[entryA.typeName] || 999;
                  const orderB = orderMap[entryB.typeName] || 999;

                  // Sort by order
                  return orderA - orderB;
                })
                .map(([key, entry]) => {
                  const { phases, minHeight: maxHeight, typeName, type } = entry;
                  const subType = (entry as any).subType; // Cast to any to access optional subType
                  const buildingBlocks = phases[phase.id] || [];

                  // Determine the correct type ID to use for adding a building block
                  // If this is an Insights subtype (has a subType), we need to pass both type and subType
                  const addTypeParam = subType ? `${type}-${subType}` : type;

                  return (
                    <MapBuildingBlockCell
                      key={`block-${key}-${phase.id}`}
                      buildingBlocks={buildingBlocks}
                      height={maxHeight}
                      onEditBuildingBlock={(id, newText) =>
                        onEditAction(id.toString(), newText, "buildingBlock")
                      }
                      onDeleteBuildingBlock={(id) => onDeleteAction(id.toString(), "buildingBlock")}
                      onAddBuildingBlock={() => onAddAction(phase.id, addTypeParam)}
                      onViewExistingData={(insights) =>
                        onViewExistingData?.(insights, phase.id, type)
                      }
                      onBuildingBlockCreated={onBuildingBlockCreated}
                      phaseId={phase.id}
                      type={typeName}
                      typeId={type}
                      isPanningDisabled={isPanningDisabled}
                      mapId={mapId}
                      transformValues={transformValues || { scale: 1, positionX: 0, positionY: 0 }}
                    />
                  );
                })}
            </>
          )}
        </Stack>
      </Stack>
    </Box>
  );
};

export default MapPhaseColumn;
