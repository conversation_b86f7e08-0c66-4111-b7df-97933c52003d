import { MapCanvasActionType } from "@/context/MapCanvasProvider";
import { ActionsByTypeRole, BuildingBlocksByType } from "@/utils/mapHelper";
import { useRouter } from "next/navigation";
import { useMemo } from "react";

interface UseUIStateHandlersProps {
  dispatch: React.Dispatch<any>;
  state: any;
  actionsByTypeRole: ActionsByTypeRole;
  buildingBlocksByType: BuildingBlocksByType;
}

export const useUIStateHandlers = ({
  dispatch,
  state,
  actionsByTypeRole,
  buildingBlocksByType,
}: UseUIStateHandlersProps) => {
  const router = useRouter();

  const handleBack = () => {
    router.push("/journeys");
  };

  const handleStoryModeToggle = () => {
    dispatch({
      type: MapCanvasActionType.TOGGLE_STORY_MODE,
      payload: !state.isStoryMode,
    });
  };

  const handleRoleTypeToggle = (roleType: string) => {
    dispatch({
      type: MapCanvasActionType.TOGGLE_ROLE_TYPE,
      payload: { roleType },
    });
  };

  const handleBuildingBlockTypeToggle = (buildingBlockType: string) => {
    dispatch({
      type: MapCanvasActionType.TOGGLE_BUILDING_BLOCK_TYPE,
      payload: { buildingBlockType },
    });
  };

  const filteredActionsByTypeRole = useMemo(() => {
    const filtered: ActionsByTypeRole = {};
    Object.entries(actionsByTypeRole).forEach(([key, value]) => {
      if (state.visibleRoleTypes.includes(key)) {
        filtered[key] = value;
      }
    });
    return filtered;
  }, [actionsByTypeRole, state.visibleRoleTypes]);

  const filteredBuildingBlocksByType = useMemo(() => {
    const filtered: BuildingBlocksByType = {};
    Object.entries(buildingBlocksByType).forEach(([key, value]) => {
      // Check if this is a composite key (for Insights subtypes)
      const isCompositeKey = typeof key === "string" && key.includes("-");

      if (isCompositeKey) {
        // For composite keys (Insights subtypes), check if the subtype name is in the visible types
        if (state.visibleBuildingBlockTypes.includes(value.typeName)) {
          filtered[key] = value;
        }
      } else {
        // For regular building block types, check if the type name is in the visible types
        if (state.visibleBuildingBlockTypes.includes(value.typeName)) {
          filtered[key] = value;
        }
      }
    });
    return filtered;
  }, [buildingBlocksByType, state.visibleBuildingBlockTypes]);

  return {
    handleBack,
    handleStoryModeToggle,
    handleRoleTypeToggle,
    handleBuildingBlockTypeToggle,
    filteredActionsByTypeRole,
    filteredBuildingBlocksByType,
  };
};
