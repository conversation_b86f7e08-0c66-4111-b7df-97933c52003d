import { MapCanvasActionType } from "@/context/MapCanvasProvider";
import mapService from "@/services/MapService";
import { useCallback } from "react";

interface UseBuildingBlockHandlersProps {
  dispatch: any;
  mapId: string;
  buildingBlocks: any[];
}

export const useBuildingBlockHandlers = ({
  dispatch,
  mapId,
  buildingBlocks,
}: UseBuildingBlockHandlersProps) => {
  // Handle viewing existing insights data
  const handleViewExistingData = useCallback(
    async (insights: any[], phaseId: number, typeId: number) => {
      try {
        const newBuildingBlocks: any[] = [];

        for (const insight of insights) {
          const buildingBlockData = {
            type: typeId,
            sub_type: insight.sub_type?.id || insight.sub_type,
            phase: phaseId,
            block: insight.id,
            desc: insight.desc,
            name: insight.name,
            order: insight.order || 100,
          };

          await mapService.postBuildingBlock(buildingBlockData, mapId);

          const buildingBlockWithPhase = {
            ...insight,
            phase: phaseId,
            type: typeId,
            sub_type: insight.sub_type?.id || insight.sub_type,
            vote: (insight as any).vote,
          };

          (buildingBlockWithPhase as any).originalBuildingBlockId = insight.id;
          newBuildingBlocks.push(buildingBlockWithPhase);
        }

        dispatch({
          type: MapCanvasActionType.SET_BUILDING_BLOCKS,
          payload: [...buildingBlocks, ...newBuildingBlocks],
        });
      } catch (error) {
        console.error("Failed to import insights:", error);
      }
    },
    [dispatch, mapId, buildingBlocks]
  );

  // Handle building block creation
  const handleBuildingBlockCreated = useCallback(
    (buildingBlock: any) => {
      dispatch({
        type: MapCanvasActionType.ADD_BUILDING_BLOCK,
        payload: { buildingBlock },
      });
    },
    [dispatch]
  );

  return {
    handleViewExistingData,
    handleBuildingBlockCreated,
  };
};
