import * as Y from "yjs";
import { WebrtcProvider } from "y-webrtc";
import { useEffect, useRef, useState, useCallback } from "react";
import { MapCanvasActionType } from "@/context/MapCanvasProvider";
import debounce from 'lodash/debounce';

export interface UserInfo {
  id: string;
  name: string;
  color: string;
  cursor?: { x: number; y: number };
}
interface UseYjsSyncProps {
  dispatch: any;
  roomId: string;
  userInfo: UserInfo;
}

export const useYjsSync = ({ dispatch, roomId, userInfo }: UseYjsSyncProps) => {
  const isYjsEnabled = process.env.NEXT_PUBLIC_ENABLE_YJS === 'true';
  const ydoc = useRef<Y.Doc>(null);
  const provider = useRef<WebrtcProvider>(null);
  const [activeUsers, setActiveUsers] = useState<UserInfo[]>([]);
  const isLocalUpdate = useRef(false);

  // Debounced update function
  const debouncedUpdate = useCallback(
    debounce((yActions, yPhases, actions, phases) => {
      if (!isLocalUpdate.current) {
        isLocalUpdate.current = true;
        yActions?.delete(0, yActions.length);
        yActions?.insert(0, actions);
        yPhases?.delete(0, yPhases.length);
        yPhases?.insert(0, phases);
        isLocalUpdate.current = false;
      }
    }, 100),
    [debounce]
  );

  useEffect(() => {
    if (!isYjsEnabled) return;

    ydoc.current = new Y.Doc();
    
    // Configure WebRTC provider with custom signaling servers
    provider.current = new WebrtcProvider(`mulapin-canvas-${roomId}`, ydoc.current, {
      signaling: [
        "wss://signaling.yjs.dev",
        "wss://y-webrtc-signaling-eu.herokuapp.com",
        "wss://y-webrtc-signaling-us.herokuapp.com"
      ],
      // Fallback configuration
      maxConns: 20, 
      filterBcConns: false, // Don't filter broadcast connections
    });

    provider.current.awareness.setLocalState(userInfo);

    // Create shared data structures
    const yActions = ydoc.current.getArray("actions");
    const yState = ydoc.current.getMap("state");

    // Track active users
    provider.current.awareness.on("change", () => {
      const states = Array.from(provider.current.awareness.getStates().values());
      setActiveUsers(states as UserInfo[]);
    });

    // Observe action changes
    yActions.observe(() => {
      dispatch({
        type: MapCanvasActionType.SET_DATA,
        payload: { actions: yActions.toArray() }
      });
    });

    // Observe individual operations
    yState.observe(event => {
      if (isLocalUpdate.current) return;
      
      const changes = event.changes;
      changes.keys.forEach((change, key) => {
        if (change.action === 'add' || change.action === 'update') {
          const value = yState.get(key);
          dispatch({ type: key as MapCanvasActionType, payload: value });
        }
      });
    });

    return () => {
      provider.current?.destroy();
      ydoc.current?.destroy();
    };
  }, [isYjsEnabled, roomId, userInfo, dispatch]);

  const updateSharedData = useCallback((actions: any[], phases: any[]) => {
    if (!isYjsEnabled || !ydoc.current) return;

    const yActions = ydoc.current.getArray("actions");
    const yPhases = ydoc.current.getArray("phases");

    debouncedUpdate(yActions, yPhases, actions, phases);
  }, [isYjsEnabled, debouncedUpdate]);

  const syncAction = useCallback((type: MapCanvasActionType, payload?: any) => {
    if (!isYjsEnabled || !ydoc.current || isLocalUpdate.current) return;
    
    const yState = ydoc.current.getMap("state");
    isLocalUpdate.current = true;
    yState.set(type, payload);
    isLocalUpdate.current = false;
  }, [isYjsEnabled]);

  return { 
    activeUsers: isYjsEnabled ? activeUsers : [],
    updateSharedData,
    syncAction,
    isYjsEnabled
  };
};
