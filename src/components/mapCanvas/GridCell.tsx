import { useMapCanvas } from "@/context/MapCanvasProvider";
import { Phase, VisibleRow } from "@/types/grid.types";
import { Box } from "@mui/material";
import React from "react";
import { ActivityCell } from "./ActivityCell";
import MomentCard from "./MomentCard";
import { SystemCell } from "./SystemCell";

interface GridCellProps {
  row: VisibleRow;
  phase: Phase;
  isSelected: boolean;
  cellWidth: number;
  cellHeight: number;
  columnIndex?: number;
}

export const GridCell: React.FC<GridCellProps> = ({
  row,
  phase,
  isSelected,
  cellWidth,
  cellHeight,
  columnIndex = 0,
}) => {
  const { state } = useMapCanvas();

  // Determine background color based on column index
  const getBackgroundColor = () => {
    if (isSelected) return "primary.dark";
    return columnIndex % 2 === 0 ? "map.evenColBackground" : "map.oddColBackground";
  };

  // Helper function to get moment data for this phase
  const getMomentForPhase = (phaseId: string | number) => {
    const phaseIdStr = typeof phaseId === "number" ? phaseId.toString() : phaseId;
    const moments = Array.isArray(state.moments) ? state.moments : [];
    return moments.find(
      (moment: any) =>
        Array.isArray(state.phases) &&
        state.phases.some(
          (phase: any) =>
            phase.phase_id === phaseIdStr &&
            Array.isArray(phase.moment_ids) &&
            phase.moment_ids.includes(moment.moment_id)
        )
    );
  };

  // Helper function to get activities for this moment and persona type
  const getActivitiesForMomentAndPersona = (momentId: string | number, personaType: string) => {
    const momentIdStr = typeof momentId === "number" ? momentId.toString() : momentId;
    const moments = Array.isArray(state.moments) ? state.moments : [];
    const moment = moments.find((m: any) => m.moment_id === momentIdStr);
    if (!moment) return [];
    
    const activityType = personaType.toLowerCase().replace(/_/g, "").replace(/\s+/g, "");
    const typeMapping: Record<string, keyof typeof moment.activities> = {
      'customer': 'customer',
      'frontstage': 'front_stage', 
      'backstage': 'back_stage',
      'system': 'system'
    };
    
    const mappedType = typeMapping[activityType];
    return mappedType ? moment.activities[mappedType] : [];
  };

  const renderCellContent = () => {
    switch (row.type) {
      case "story":
        const moment = getMomentForPhase(phase.id || 0);
        if (moment) {
          return (
            <MomentCard
              id={parseInt(moment.moment_id) || 0}
              name={moment.name || "Untitled Moment"}
              desc={moment.description || ""}
              image={moment.image_url}
              components={[]}
              onEdit={(updates) => console.log("Edit moment:", updates)}
              onImageUpdate={(momentId, imageUrl) =>
                console.log("Update moment image:", momentId, imageUrl)
              }
            />
          );
        } else {
          // Return empty moment card if no moment data available
          return (
            <MomentCard
              id={0}
              name=""
              desc=""
              components={[]}
              onEdit={(updates) => console.log("Edit moment:", updates)}
              onImageUpdate={(momentId, imageUrl) =>
                console.log("Update moment image:", momentId, imageUrl)
              }
            />
          );
        }

      case "action":
      case "activity":
        const activityMoment = getMomentForPhase(phase.id || 0);
        const activities = activityMoment ? 
          getActivitiesForMomentAndPersona(activityMoment.moment_id, row.name) : [];
        return (
          <ActivityCell
            momentId={phase.id || 0}
            activityType={row.type}
            role={row.name}
            activities={Array.isArray(activities) ? activities : []}
            onEditActivity={(id, text) => console.log("Edit activity:", id, text)}
            onDeleteActivity={(id) => console.log("Delete activity:", id)}
            onAddActivity={() => console.log("Add activity")}
          />
        );

      case "block":
        return <></>;

      case "system":
        return <SystemCell row={row} phase={phase} />;

      default:
        const defaultActivityMoment = getMomentForPhase(phase.id || 0);
        const defaultActivities = defaultActivityMoment ? 
          getActivitiesForMomentAndPersona(defaultActivityMoment.moment_id, row.name) : [];
        return (
          <ActivityCell
            momentId={phase.id || 0}
            activityType="activity"
            role={row.name}
            activities={Array.isArray(defaultActivities) ? defaultActivities : []}
            onEditActivity={(id, text) => console.log("Edit activity:", id, text)}
            onDeleteActivity={(id) => console.log("Delete activity:", id)}
            onAddActivity={() => console.log("Add activity")}
          />
        );
    }
  };

  return (
    <Box
      data-cell={`${row.id}-${phase.id}`}
      sx={{
        width: cellWidth,
        height: cellHeight,
        border: "none",
        bgcolor: getBackgroundColor(),
        p: 0,
        boxSizing: "border-box",
        cursor: "default",
        borderRadius: 0,
      }}
    >
      {renderCellContent()}
    </Box>
  );
};
