import { BuildingBlock } from '@/types/grid.types';
import MoreVertIcon from "@mui/icons-material/MoreVert";
import {
  Box,
  Button,
  Card,
  IconButton,
  Menu,
  MenuItem,
  TextField,
  Typography,
} from "@mui/material";
import React, { useState } from "react";

interface BuildingBlockCardProps {
  buildingBlock: BuildingBlock;
  onClick?: () => void;
  onEdit?: (newText: string) => void;
  onDelete?: () => void;
  type?: "Painpoints" | "Opportunity" | "Operation context" | "NPS" | "CES" | "CSAT";
  variant?: "bordered" | "default";
}

const BuildingBlockCard: React.FC<BuildingBlockCardProps> = ({
  buildingBlock,
  onClick,
  onEdit,
  onDelete,
  type = "Operation context",
  variant = "default",
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editText, setEditText] = useState(buildingBlock.desc || "");
  const open = Boolean(anchorEl);

  // Determine border color based on type
  const getBorderColor = () => {
    return type === "Painpoints" ? "orange" : "green";
  };

  // Determine if this variant should have borders
  const shouldShowBorders = () => {
    return variant === "bordered" && (type === "Painpoints" || type === "Opportunity");
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleDelete = (event: React.MouseEvent) => {
    event.stopPropagation();
    if (onDelete) onDelete();
    handleClose();
  };

  const handleTextClick = (event: React.MouseEvent) => {
    event.stopPropagation();
    setIsEditing(true);
  };

  const handleTextChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setEditText(event.target.value);
  };

  const handleSave = (event: React.MouseEvent) => {
    event.stopPropagation();
    if (onEdit) onEdit(editText);
    setIsEditing(false);
  };

  const handleCancel = (event: React.MouseEvent) => {
    event.stopPropagation();
    setEditText(buildingBlock.desc || "");
    setIsEditing(false);
  };

  return (
    <Card
      onClick={onClick}
      sx={{
        backgroundColor: "background.paper",
        color: "text.primary",
        padding: 2,
        borderRadius: 2,
        width: "100%",
        minHeight: "100px",
        maxHeight: "200px",
        overflow: "auto",
        cursor: isEditing ? "default" : onClick ? "pointer" : "default",
        display: "flex",
        flexDirection: "column",
        position: "relative",
        ...(shouldShowBorders() && {
          borderLeft: `3px solid ${getBorderColor()}`,
          borderTop: `3px solid ${getBorderColor()}`,
          borderRight: "none",
          borderBottom: "none",
        }),
        "&:hover":
          !isEditing && onClick
            ? {
                backgroundColor: "action.hover",
              }
            : undefined,
      }}
    >
      <Box
        sx={{ display: "flex", flexDirection: "column", alignItems: "flex-start", mb: 1, gap: 2 }}
      >
        <Typography
          variant={variant === "bordered" ? "subtitle2" : "subtitle1"}
          fontWeight="bold"
          sx={{
            mb: variant === "bordered" ? 0.5 : 0,
            wordBreak: "break-word",
            whiteSpace: "pre-wrap",
          }}
        >
          {buildingBlock.name || `Building Block ${buildingBlock.id}`}
        </Typography>

        <Box sx={{ flex: 1, width: "100%" }}>
          {isEditing ? (
            <Box onClick={(e) => e.stopPropagation()} onMouseDown={(e) => e.stopPropagation()}>
              <TextField
                fullWidth
                multiline
                value={editText}
                onChange={handleTextChange}
                autoFocus
                variant="outlined"
                size="small"
                sx={{
                  mb: 1,
                  "& .MuiInputBase-root": {
                    cursor: "text",
                  },
                  "& .MuiInputBase-input": {
                    cursor: "text",
                    maxHeight: "150px",
                    overflow: "auto",
                  },
                }}
                onClick={(e) => e.stopPropagation()}
                onMouseDown={(e) => e.stopPropagation()}
                onKeyDown={(e) => e.stopPropagation()}
                maxRows={6}
              />
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "flex-end",
                  gap: 1,
                  position: "sticky",
                  bottom: 0,
                  backgroundColor: "background.paper",
                  pt: 1,
                  zIndex: 1,
                }}
              >
                <Button
                  size="small"
                  variant="outlined"
                  onClick={handleCancel}
                  onMouseDown={(e) => e.stopPropagation()}
                >
                  Cancel
                </Button>
                <Button
                  size="small"
                  variant="contained"
                  onClick={handleSave}
                  onMouseDown={(e) => e.stopPropagation()}
                >
                  Save
                </Button>
              </Box>
            </Box>
          ) : (
            <Typography
              variant={variant === "bordered" ? "body2" : "body1"}
              onClick={handleTextClick}
              sx={{
                wordBreak: "break-word",
                whiteSpace: "pre-wrap",
                pr: onEdit || onDelete ? 4 : 0,
                cursor: "text",
                fontSize: variant === "bordered" ? "0.75rem" : undefined,
                lineHeight: variant === "bordered" ? 1.3 : undefined,
                "&:hover": {
                  backgroundColor: "rgba(0, 0, 0, 0.04)",
                },
              }}
            >
              {buildingBlock.desc || "No description"}
            </Typography>
          )}
        </Box>

        {!isEditing && variant === "bordered" && (
          <Box sx={{ mt: 0.5 }}>
            <Typography
              variant="caption"
              sx={{
                backgroundColor: "rgba(0, 0, 0, 0.2)",
                borderRadius: "12px",
                padding: "2px 8px",
                display: "inline-block",
                fontSize: "0.7rem",
              }}
            >
              Vote: {(buildingBlock as any).vote || buildingBlock.order || 0}
            </Typography>
          </Box>
        )}

        {!isEditing && (onEdit || onDelete) && (
          <Box sx={{ position: "absolute", top: 8, right: 8 }}>
            <IconButton
              aria-label="more"
              aria-controls={open ? "building-block-menu" : undefined}
              aria-haspopup="true"
              aria-expanded={open ? "true" : undefined}
              onClick={handleMenuClick}
              size="small"
            >
              <MoreVertIcon fontSize="small" />
            </IconButton>
            <Menu
              id="building-block-menu"
              anchorEl={anchorEl}
              open={open}
              onClose={handleClose}
              onClick={(e) => e.stopPropagation()}
            >
              {onDelete && <MenuItem onClick={handleDelete}>Delete</MenuItem>}
            </Menu>
          </Box>
        )}
      </Box>
    </Card>
  );
};

export default BuildingBlockCard;