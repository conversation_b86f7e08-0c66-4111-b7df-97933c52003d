import { useMapCan<PERSON>, MapCanvasActionType } from "@/context/MapCanvasProvider";
import { Phase as ApiPhase, Action, BuildingBlock, MapRole } from "@/types/map";
import {
  ArrowBack,
  ArrowDownward,
  ArrowForward,
  ArrowUpward,
  VisibilityOff,
} from "@mui/icons-material";
import { Box, CssBaseline, Menu, MenuItem, Typography } from "@mui/material";
import React, { useEffect, useMemo, useState } from "react";
import { useGridInteractions } from "../hooks/useGridInteractions";
import { useGridState } from "../hooks/useGridState";
import { RowGroup, Stage, VisibleRow, Phase } from "../types/grid.types";
import CanvasControlToolbar from "./CanvasControlToolbar";
import FloatingMenu from "./FloatingMenu";
import { ColumnHeader } from "./grid/ColumnHeader";
import { GridCell } from "./grid/GridCell";
import { RowHeader } from "./grid/RowHeader";

// Grid configuration
const CELL_WIDTH = 770;
const STORY_CELL_HEIGHT = 525;
const DEFAULT_CELL_HEIGHT = 350;
const CELL_GAP = 4; // Gap between cells
const ROW_HEADER_WIDTH = 70;
const SUB_ROW_HEADER_WIDTH = 120;
const COL_HEADER_HEIGHT = 50;

// Data transformation functions
const transformApiPhasesToStages = (apiPhases: ApiPhase[]): Stage[] => {
  // Group phases by stage - for now we'll create a single stage with all phases
  // This can be enhanced later if API provides stage information
  if (apiPhases.length === 0) return [];
  
  return [
    {
      id: "journey-stage",
      name: "Customer Journey",
      phases: apiPhases.map(phase => ({
        id: phase.id?.toString() || Math.random().toString(),
        name: phase.name || `Phase ${phase.id}`,
        order: phase.order,
        desc: phase.desc,
        image: phase.image,
        component_ids: phase.component_ids,
      }))
    }
  ];
};

const getRowGroupsWithActions = (actions: Action[], roles: MapRole[]): RowGroup[] => {
  // Create dynamic row groups based on available role types
  const roleTypes = Array.from(new Set(roles.map(role => role.type)));
  
  const dynamicRowGroups: RowGroup[] = [
    {
      id: "story",
      name: "Story",
      type: "story",
      expandable: false,
      subRows: [],
    }
  ];

  // Add role-based row groups
  roleTypes.forEach(roleType => {
    if (roleType) {
      dynamicRowGroups.push({
        id: roleType.toLowerCase().replace(/\s+/g, ''),
        name: roleType,
        type: "action",
        expandable: true,
        subRows: [
          { 
            id: `${roleType.toLowerCase().replace(/\s+/g, '')}-actions`, 
            name: "Actions", 
            parentId: roleType.toLowerCase().replace(/\s+/g, ''), 
            type: "action" 
          },
          { 
            id: `${roleType.toLowerCase().replace(/\s+/g, '')}-feelings`, 
            name: "Feelings", 
            parentId: roleType.toLowerCase().replace(/\s+/g, ''), 
            type: "action" 
          },
        ],
      });
    }
  });

  // Add standard building block rows
  dynamicRowGroups.push(
    { id: "painpoints", name: "Painpoints", type: "block", expandable: false, subRows: [] },
    { id: "opportunity", name: "Opportunity", type: "block", expandable: false, subRows: [] },
    { id: "voc", name: "Voice of Customer", type: "block", expandable: false, subRows: [] },
    { id: "context", name: "Operation Context", type: "block", expandable: false, subRows: [] },
    { id: "analysis", name: "Analysis", type: "block", expandable: false, subRows: [] }
  );

  return dynamicRowGroups;
};

// Default data configuration - will be replaced with actual data from props/context
const defaultStages: Stage[] = [
  {
    id: "1",
    name: "stage",
    phases: [
      { id: "discovery", name: "Discovery" },
      { id: "research", name: "Research" },
      { id: "compare", name: "Compare" },
    ],
  },
  {
    id: "2",
    name: "stage",
    phases: [
      { id: "evaluate", name: "Evaluate" },
      { id: "trial", name: "Trial" },
    ],
  },
  {
    id: "3",
    name: "stage",
    phases: [
      { id: "buy", name: "Buy" },
      { id: "receive", name: "Receive" },
    ],
  },
  {
    id: "4",
    name: "stage",
    phases: [
      { id: "use", name: "Use" },
      { id: "support", name: "Support" },
      { id: "renew", name: "Renew" },
    ],
  },
];

const defaultRowGroups: RowGroup[] = [
  {
    id: "story",
    name: "Story",
    type: "story",
    expandable: false,
    subRows: [],
  },
  {
    id: "customer",
    name: "Customer",
    type: "action",
    expandable: true,
    subRows: [
      { id: "customer-actions", name: "Actions", parentId: "customer", type: "action" },
      { id: "customer-feelings", name: "Feelings", parentId: "customer", type: "action" },
    ],
  },
  {
    id: "frontStage",
    name: "Front Stage",
    type: "action",
    expandable: true,
    subRows: [
      { id: "front-digital", name: "Digital", parentId: "frontStage", type: "action" },
      { id: "front-physical", name: "Physical", parentId: "frontStage", type: "action" },
    ],
  },
  {
    id: "backStage",
    name: "Back Stage",
    type: "action",
    expandable: true,
    subRows: [
      { id: "back-systems", name: "Systems", parentId: "backStage", type: "system" },
      { id: "back-processes", name: "Processes", parentId: "backStage", type: "system" },
    ],
  },
  { id: "painpoints", name: "Painpoints", type: "block", expandable: false, subRows: [] },
  { id: "opportunity", name: "Opportunity", type: "block", expandable: false, subRows: [] },
  { id: "voc", name: "Voice of Customer", type: "block", expandable: false, subRows: [] },
  { id: "context", name: "Operation Context", type: "block", expandable: false, subRows: [] },
  { id: "analysis", name: "Analysis", type: "block", expandable: false, subRows: [] },
];

interface CustomerJourneyMapProps {
  map?: any; // TODO: Add proper type
  stages?: Stage[];
  rowGroups?: RowGroup[];
}

export const CustomerJourneyMap: React.FC<CustomerJourneyMapProps> = ({
  map,
  stages: propStages,
  rowGroups: propRowGroups,
}) => {
  // Get data from MapCanvasProvider
  const { state, dispatch } = useMapCanvas();
  
  // Transform API data to grid format
  const apiStages = useMemo(() => {
    return transformApiPhasesToStages(state.phases);
  }, [state.phases]);
  
  const apiRowGroups = useMemo(() => {
    return getRowGroupsWithActions(state.actions, state.roles);
  }, [state.actions, state.roles]);
  
  // Use API data first, then props, then defaults
  const actualStages = useMemo(() => {
    if (apiStages.length > 0) return apiStages;
    if (propStages && propStages.length > 0) return propStages;
    return defaultStages;
  }, [apiStages, propStages]);
  
  const actualRowGroups = useMemo(() => {
    if (state.phases.length > 0 || state.actions.length > 0) return apiRowGroups;
    if (propRowGroups && propRowGroups.length > 0) return propRowGroups;
    return defaultRowGroups;
  }, [apiRowGroups, propRowGroups, state.phases.length, state.actions.length]);
  const {
    scale,
    setScale,
    scrollPos,
    setScrollPos,
    mode,
    setMode,
    expandedRows,
    toggleRow,
    hiddenColumns,
    hiddenRows,
    toggleRowVisibility,
    toggleColumnVisibility,
    isStoryMode,
    setIsStoryMode,
  } = useGridState();

  const [dropdownAnchor, setDropdownAnchor] = useState<null | HTMLElement>(null);
  const [dropdownContext, setDropdownContext] = useState<{ type: string; id: string } | null>(null);
  const [showSubHeaders, setShowSubHeaders] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize data from map prop (similar to original MapCanvas)
  useEffect(() => {
    if (map && !isInitialized) {
      console.log("Initializing CustomerJourneyMap with map data:", map);
      dispatch({
        type: MapCanvasActionType.SET_DATA,
        payload: {
          ...map,
          id: map.id,
        },
      });
      setIsInitialized(true);
    }
  }, [map, dispatch, isInitialized]);

  // Helper function to get cell height based on row type
  const getCellHeight = (rowType: string) => {
    return rowType === "story" ? STORY_CELL_HEIGHT : DEFAULT_CELL_HEIGHT;
  };

  // Calculate visible rows based on expanded state and sub header toggle
  const visibleRows = useMemo(() => {
    const rows: VisibleRow[] = [];
    actualRowGroups.forEach((group) => {
      if (hiddenRows.includes(group.id)) return;

      // For Customer, Front Stage, Back Stage - check if sub headers should be shown
      const hasToggleableSubRows = ["customer", "frontStage", "backStage"].includes(group.id);

      if (
        hasToggleableSubRows &&
        showSubHeaders &&
        group.subRows.length > 0 &&
        expandedRows[group.id]
      ) {
        // Show sub rows when toggle is enabled and group is expanded
        group.subRows.forEach((subRow, index) => {
          if (!hiddenRows.includes(subRow.id)) {
            rows.push({
              ...subRow,
              isFirstSubRow: index === 0,
              isLastSubRow: index === group.subRows.length - 1,
              parentSpan: group.subRows.filter((sr) => !hiddenRows.includes(sr.id)).length,
            });
          }
        });
      } else {
        // Show parent row (always show for non-toggleable groups, or when sub headers are hidden)
        rows.push(group);
      }
    });
    return rows;
  }, [actualRowGroups, hiddenRows, expandedRows, showSubHeaders]);

  // Calculate all phases
  const allPhases = useMemo(() => actualStages.flatMap((stage) => stage.phases), [actualStages]);

  const visiblePhases = useMemo(
    () => allPhases.filter((_, index) => !hiddenColumns.includes(index.toString())),
    [allPhases, hiddenColumns]
  );

  const visibleRowIds = useMemo(
    () => actualRowGroups.filter((row) => !hiddenRows.includes(row.id)).map((row) => row.id),
    [actualRowGroups, hiddenRows]
  );

  const totalHeaderWidth = ROW_HEADER_WIDTH + SUB_ROW_HEADER_WIDTH;

  // Grid interactions
  const { scrollAreaRef, handleWheel, handleMouseDown, handleScroll } = useGridInteractions({
    mode,
    scale,
    onScroll: setScrollPos,
    onCellSelect: () => {
      // Disable cell selection
    },
  });

  // Zoom controls
  const zoomIn = () => setScale(Math.min(scale * 1.2, 2));
  const zoomOut = () => setScale(Math.max(scale * 0.8, 0.1));
  const fitToScreen = () => {
    // Reset scale and scroll position, also reset scroll area scroll position
    setScale(0.8);
    setScrollPos({ x: 0, y: 0 });
    // Reset the actual scroll area scroll position
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollLeft = 0;
      scrollAreaRef.current.scrollTop = 0;
    }
  };

  // Dropdown handlers
  const handleDropdownOpen = (event: React.MouseEvent, type: string, id: string) => {
    event.stopPropagation();
    setDropdownAnchor(event.currentTarget as HTMLElement);
    setDropdownContext({ type, id });
  };

  const handleDropdownClose = () => {
    setDropdownAnchor(null);
    setDropdownContext(null);
  };

  const handleDropdownAction = (action: string) => {
    if (dropdownContext) {
      if (action === "hide") {
        if (dropdownContext.type === "column") {
          toggleColumnVisibility(dropdownContext.id);
        } else {
          toggleRowVisibility(dropdownContext.id);
        }
      }
      // Handle other actions like move up/down/left/right
    }
    handleDropdownClose();
  };

  // Phase editing handlers
  const handleEditPhase = (phase: { id: string | number; name: string }) => {
    // Find the phase in the state and update it
    const phaseId = typeof phase.id === 'string' ? parseInt(phase.id) : phase.id;
    const existingPhase = state.phases.find(p => p.id === phaseId);
    
    if (existingPhase) {
      // Update the phase using the MapCanvasProvider's dispatch
      // This would typically integrate with the phase actions from usePhaseActions
      console.log("Edit phase:", phase, "Current phase:", existingPhase);
      // For now, just log - full implementation would use dispatch
    }
  };

  const handleDeletePhase = (id: string | number) => {
    const phaseId = typeof id === 'string' ? parseInt(id) : id;
    console.log("Delete phase:", phaseId);
    // This would integrate with the delete phase action
    // For now, just log - full implementation would use dispatch
  };

  return (
    <>
      <CssBaseline />
      <Box
        sx={{
          width: "100vw",
          height: "100vh",
          bgcolor: "background.default",
          display: "flex",
          flexDirection: "column",
        }}
      >
        {/* FloatingMenu */}
        <FloatingMenu
          name={map?.name || "Customer Journey Map"}
          mapId={map?.id || ""}
          back={() => window.history.back()}
          status={map?.status || "Draft"}
        />

        {/* Grid Container */}
        <Box
          sx={{
            flex: 1,
            position: "relative",
            overflow: "hidden",
            pt: "140px",
            pl: "calc(var(--sidebar-width, 240px) + 200px)",
            pr: "200px",
            pb: "15px",
          }}
        >
          {/* Canvas Control Toolbar */}
          <CanvasControlToolbar
            scale={scale}
            mode={mode}
            isStoryMode={isStoryMode}
            visibleRows={visibleRowIds}
            onModeChange={setMode}
            onZoomIn={zoomIn}
            onZoomOut={zoomOut}
            onFitToScreen={fitToScreen}
            onToggleStoryMode={() => setIsStoryMode(!isStoryMode)}
            onRowToggle={toggleRowVisibility}
            showSubHeaders={showSubHeaders}
            onToggleSubHeaders={() => setShowSubHeaders(!showSubHeaders)}
          />

          {/* Fixed top-left corner */}
          <Box
            sx={{
              position: "absolute",
              top: "85px",
              left: "120px",
              width: totalHeaderWidth * scale,
              height: COL_HEADER_HEIGHT * scale,
              zIndex: 10,
              overflow: "hidden",
            }}
          >
            <Box
              sx={{
                transform: `scale(${scale})`,
                transformOrigin: "top left",
                width: totalHeaderWidth,
                height: COL_HEADER_HEIGHT,
                bgcolor: "map.header",
                borderBottom: "4px solid",
                borderColor: "background.paper",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <Typography variant="body2" sx={{ fontWeight: 600, color: "text.contrast" }}>
                Phase
              </Typography>
            </Box>
          </Box>

          {/* Fixed column headers */}
          <Box
            sx={{
              position: "absolute",
              top: "85px",
              left: `calc(${totalHeaderWidth * scale}px + 120px)`,
              right: 0,
              height: COL_HEADER_HEIGHT * scale,
              zIndex: 9,
              overflow: "hidden",
            }}
          >
            <Box
              sx={{
                transform: `translateX(-${scrollPos.x}px)`,
                width: visiblePhases.length * CELL_WIDTH * scale,
                height: "100%",
              }}
            >
              <Box
                sx={{
                  transform: `scale(${scale})`,
                  transformOrigin: "top left",
                  display: "flex",
                  height: COL_HEADER_HEIGHT,
                  width: visiblePhases.length * CELL_WIDTH,
                }}
              >
                {visiblePhases.map((phase, columnIndex) => {
                  const stage = actualStages.find((s) => s.phases.some((p) => p.id === phase.id))!;
                  return (
                    <ColumnHeader
                      key={phase.id}
                      phase={{
                        id: phase.id || `phase-${columnIndex}`,
                        name: phase.name || "Phase",
                      }}
                      stage={stage}
                      width={CELL_WIDTH}
                      height={COL_HEADER_HEIGHT}
                      scale={scale}
                      onEditPhase={handleEditPhase}
                      onDeletePhase={handleDeletePhase}
                    />
                  );
                })}
              </Box>
            </Box>
          </Box>

          {/* Fixed row headers */}
          <Box
            sx={{
              position: "absolute",
              top: `calc(${COL_HEADER_HEIGHT * scale}px + 85px)`,
              left: "120px",
              bottom: 0,
              width: totalHeaderWidth * scale,
              zIndex: 9,
              overflow: "hidden",
            }}
          >
            <Box
              sx={{
                width: totalHeaderWidth * scale,
                height:
                  visibleRows.reduce(
                    (total, row) => total + getCellHeight(row.type) + CELL_GAP,
                    0
                  ) * scale,
              }}
            >
              <Box
                sx={{
                  transform: `translateY(-${scrollPos.y}px) scale(${scale})`,
                  transformOrigin: "top left",
                  width: totalHeaderWidth,
                  height: visibleRows.reduce(
                    (total, row) => total + getCellHeight(row.type) + CELL_GAP,
                    0
                  ),
                  display: "flex",
                  flexDirection: "column",
                  gap: `${CELL_GAP}px`,
                }}
              >
                {visibleRows.map((row) => {
                  const isParentRow = !row.parentId;
                  const parentGroup = row.parentId
                    ? actualRowGroups.find((g) => g.id === row.parentId)
                    : null;
                  const hasSubRows = isParentRow
                    ? row.subRows?.length > 0
                    : parentGroup?.subRows?.length > 0;

                  return (
                    <RowHeader
                      key={row.id}
                      row={row}
                      parentGroup={parentGroup}
                      isFirstSubRow={row.isFirstSubRow}
                      parentSpan={row.parentSpan}
                      hasSubRows={hasSubRows || false}
                      isExpanded={expandedRows[isParentRow ? row.id : parentGroup?.id || ""]}
                      cellHeight={getCellHeight(row.type)}
                      totalHeaderWidth={totalHeaderWidth}
                      showSubHeaders={showSubHeaders}
                      onToggle={
                        hasSubRows
                          ? () => toggleRow(isParentRow ? row.id : parentGroup?.id || "")
                          : undefined
                      }
                      onMenuClick={(e, rowId) => handleDropdownOpen(e, "row", rowId)}
                    />
                  );
                })}
              </Box>
            </Box>
          </Box>

          {/* Scrollable content area */}
          <Box
            ref={scrollAreaRef}
            sx={{
              position: "absolute",
              top: `calc(${COL_HEADER_HEIGHT * scale}px + 85px)`,
              left: `calc(${totalHeaderWidth * scale}px + 120px)`,
              right: 0,
              bottom: 0,
              overflow: "auto",
              cursor: mode === "pan" ? "grab" : "default",
              "&:active": {
                cursor: mode === "pan" ? "grabbing" : "default",
              },
            }}
            onWheel={(e) => handleWheel(e.nativeEvent)}
            onMouseDown={handleMouseDown}
            onScroll={handleScroll}
          >
            <Box
              sx={{
                width: visiblePhases.length * CELL_WIDTH * scale + 100,
                height:
                  visibleRows.reduce(
                    (total, row) => total + getCellHeight(row.type) + CELL_GAP,
                    0
                  ) *
                    scale +
                  100,
              }}
            >
              <Box
                sx={{
                  transform: `scale(${scale})`,
                  transformOrigin: "top left",
                  width: visiblePhases.length * CELL_WIDTH,
                  height: visibleRows.reduce(
                    (total, row) => total + getCellHeight(row.type) + CELL_GAP,
                    0
                  ),
                  display: "flex",
                  flexDirection: "column",
                  gap: `${CELL_GAP}px`,
                }}
              >
                {visibleRows.map((row) => (
                  <Box
                    key={row.id}
                    sx={{
                      display: "flex",
                    }}
                  >
                    {visiblePhases.map((phase, columnIndex) => {
                      const stage = actualStages.find((s) =>
                        s.phases.some((p) => p.id === phase.id)
                      )!;
                      return (
                        <GridCell
                          key={`${row.id}-${phase.id}`}
                          row={row}
                          phase={phase}
                          stage={stage}
                          isSelected={false}
                          cellWidth={CELL_WIDTH}
                          cellHeight={getCellHeight(row.type)}
                          columnIndex={columnIndex}
                        />
                      );
                    })}
                  </Box>
                ))}
              </Box>
            </Box>
          </Box>

          {/* Dropdown Menu */}
          <Menu
            anchorEl={dropdownAnchor}
            open={Boolean(dropdownAnchor)}
            onClose={handleDropdownClose}
            anchorOrigin={{ vertical: "bottom", horizontal: "left" }}
          >
            {dropdownContext?.type === "column" ? (
              <>
                <MenuItem onClick={() => handleDropdownAction("moveLeft")}>
                  <ArrowBack fontSize="small" sx={{ mr: 1 }} />
                  Move Left
                </MenuItem>
                <MenuItem onClick={() => handleDropdownAction("moveRight")}>
                  <ArrowForward fontSize="small" sx={{ mr: 1 }} />
                  Move Right
                </MenuItem>
              </>
            ) : (
              <>
                <MenuItem onClick={() => handleDropdownAction("moveUp")}>
                  <ArrowUpward fontSize="small" sx={{ mr: 1 }} />
                  Move Up
                </MenuItem>
                <MenuItem onClick={() => handleDropdownAction("moveDown")}>
                  <ArrowDownward fontSize="small" sx={{ mr: 1 }} />
                  Move Down
                </MenuItem>
              </>
            )}
            <MenuItem onClick={() => handleDropdownAction("hide")}>
              <VisibilityOff fontSize="small" sx={{ mr: 1 }} />
              Hide
            </MenuItem>
          </Menu>
        </Box>
      </Box>
    </>
  );
};
