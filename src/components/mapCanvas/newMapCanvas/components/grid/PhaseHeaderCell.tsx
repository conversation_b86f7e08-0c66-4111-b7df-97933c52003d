import CheckIcon from "@mui/icons-material/Check";
import CloseIcon from "@mui/icons-material/Close";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import { Box, Card, IconButton, TextField, Tooltip, Typography } from "@mui/material";
import React, { useState } from "react";

export interface PhaseHeaderCellProps {
  /** Phase data */
  phase: {
    id: string | number;
    name: string;
  };
  /** Stage data for context */
  stage: {
    id: string;
    name: string;
  };
  /** Width of the header cell */
  width?: number;
  /** Height of the header cell */
  height?: number;
  /** Callback when phase is edited */
  onEditPhase?: (phase: { id: string | number; name: string }) => void;
  /** Callback when phase is deleted */
  onDeletePhase?: (id: string | number) => void;
  /** Scale factor for responsive sizing */
  scale?: number;
  /** Column index for alternating background colors */
  columnIndex?: number;
}

export const PhaseHeaderCell: React.FC<PhaseHeaderCellProps> = ({
  phase,
  stage,
  width = 150,
  height = 50,
  onEditPhase,
  onDeletePhase,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [label, setLabel] = useState(phase.name);
  const [tempLabel, setTempLabel] = useState(phase.name);

  const handleEditClick = () => setIsEditing(true);

  const handleCancelClick = () => {
    setTempLabel(label);
    setIsEditing(false);
  };

  const handleSaveClick = () => {
    setLabel(tempLabel);
    onEditPhase?.({ id: phase.id, name: tempLabel });
    setIsEditing(false);
  };

  // Use map.header background color
  const getBackgroundColor = () => {
    return "map.header";
  };

  return (
    <Card
      sx={{
        width,
        height,
        backgroundColor: getBackgroundColor(),
        p: 1,
        ml: 0.5,
        borderBottom: "4px solid",
        borderColor: "background.paper",
        borderRadius: 0,
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        boxSizing: "border-box",
        position: "relative",
      }}
    >
      {isEditing ? (
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            width: "100%",
            gap: 0.5,
          }}
        >
          <TextField
            value={tempLabel}
            onChange={(e) => setTempLabel(e.target.value)}
            variant="outlined"
            size="small"
            autoFocus
            sx={{
              width: "60%",
              "& .MuiInputBase-input": {
                fontSize: "0.75rem",
                p: 0.5,
              },
            }}
          />
          <IconButton onClick={handleSaveClick} size="small">
            <CheckIcon fontSize="small" />
          </IconButton>
          <IconButton onClick={handleCancelClick} size="small">
            <CloseIcon fontSize="small" />
          </IconButton>
        </Box>
      ) : (
        <Tooltip
          title={
            <Box sx={{ display: "flex", gap: 0.5 }}>
              <IconButton onClick={handleEditClick} size="small">
                <EditIcon fontSize="small" />
              </IconButton>
              {onDeletePhase && (
                <IconButton onClick={() => onDeletePhase(phase.id)} size="small">
                  <DeleteIcon fontSize="small" />
                </IconButton>
              )}
            </Box>
          }
          arrow
          placement="top"
        >
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              justifyContent: "center",
              width: "100%",
              cursor: "pointer",
            }}
          >
            <Typography
              variant="body2"
              sx={{
                fontWeight: 600,
                color: "text.contrast",
                textAlign: "center",
                lineHeight: 1.2,
              }}
            >
              {stage.name}
            </Typography>
          </Box>
        </Tooltip>
      )}
    </Card>
  );
};

export default PhaseHeaderCell;
