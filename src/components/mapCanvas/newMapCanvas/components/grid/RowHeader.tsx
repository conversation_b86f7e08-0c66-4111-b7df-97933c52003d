import { ChevronRight, ExpandMore, MoreHoriz } from "@mui/icons-material";
import { Box, IconButton, Typography } from "@mui/material";
import React from "react";
import { RowGroup, VisibleRow } from "../../types/grid.types";

interface RowHeaderProps {
  row: VisibleRow;
  parentGroup?: RowGroup | null;
  isFirstSubRow?: boolean;
  parentSpan?: number;
  hasSubRows: boolean;
  isExpanded?: boolean;
  cellHeight: number;
  totalHeaderWidth: number;
  onToggle?: () => void;
  onMenuClick: (event: React.MouseEvent, rowId: string) => void;
  showSubHeaders?: boolean;
}

export const RowHeader: React.FC<RowHeaderProps> = ({
  row,
  parentGroup,
  isFirstSubRow,
  parentSpan = 1,
  hasSubRows,
  isExpanded,
  cellHeight,
  totalHeaderWidth,
  onToggle,
  onMenuClick,
  showSubHeaders = false,
}) => {
  const isParentRow = !row.parentId;
  
  // For toggleable rows (Customer, Front Stage, Back Stage), only show vertical parent cell when sub headers are enabled
  const isToggleableRow = ['customer', 'frontStage', 'backStage'].includes(row.id);
  const shouldShowVerticalParent = hasSubRows && (!isToggleableRow || showSubHeaders);

  return (
    <Box
      sx={{
        display: "flex",
        position: "relative",
        bgcolor: "map.header",
        borderBottom: "1px solid",
        borderRight: "1px solid",
        borderColor: "transparent",
        height: cellHeight,
        width: totalHeaderWidth,
      }}
    >
      {/* Vertical parent cell */}
      {(isParentRow || isFirstSubRow) && shouldShowVerticalParent && (
        <Box
          sx={{
            width: 56,
            height: cellHeight * (isParentRow ? 1 : parentSpan) + 4,
            position: isFirstSubRow ? "absolute" : "relative",
            top: 0,
            left: 0,
            zIndex: 10,
            bgcolor: "map.header",
            borderRight: "4px solid",
            borderColor: "background.paper",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <Box
            sx={{
              writingMode: "vertical-rl",
              textOrientation: "mixed",
              transform: "rotate(180deg)",
            }}
          >
            <Typography variant="caption" sx={{ fontWeight: 600, color: "text.contrast" }}>
              {(isParentRow ? row.name : parentGroup?.name)?.toUpperCase()}
            </Typography>
          </Box>
          {onToggle && (
            <IconButton
              size="small"
              onClick={onToggle}
              sx={{
                position: "absolute",
                top: 4,
                right: 4,
                p: 0.5,
                color: "grey.400",
                "&:hover": { bgcolor: "grey.800" },
              }}
            >
              {isExpanded ? <ExpandMore fontSize="small" /> : <ChevronRight fontSize="small" />}
            </IconButton>
          )}
        </Box>
      )}

      {/* Detail cell */}
      <Box
        sx={{
          flex: 1,
          p: 2,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          ml: !isParentRow && shouldShowVerticalParent ? "64px" : (isParentRow && shouldShowVerticalParent ? "64px" : 0),
          bgcolor: "map.header",
          borderRadius: 1,
          position: "relative",
        }}
      >
        <Typography
          variant={"body2"}
          sx={{
            fontWeight: 600,
            color: "text.contrast",
            textAlign: "center",
            textTransform: "uppercase",
          }}
        >
          {isParentRow ? (shouldShowVerticalParent ? "" : row.name) : row.name}
        </Typography>
        <IconButton
          size="small"
          className="dropdown-trigger"
          onClick={(e) => onMenuClick(e, row.id)}
          sx={{
            position: "absolute",
            right: 8,
            top: 8,
            p: 0.5,
            color: "text.contrast",
            opacity: 0.7,
            "&:hover": { 
              opacity: 1,
              bgcolor: "rgba(255, 255, 255, 0.1)",
            },
          }}
        >
          <MoreHoriz fontSize="small" />
        </IconButton>
      </Box>
    </Box>
  );
};
