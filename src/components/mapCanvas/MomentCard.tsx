import { useMapCanvas } from "@/context/MapCanvasProvider";
import aiService from "@/services/AIService";
import mapService, { ComponentMatch, FrameworkMatch } from "@/services/MapService";
import { MomentDetails } from "@/types/api/client";
import { Component } from "@/types/map";
import { AccountTree, MoreVert } from "@mui/icons-material";
import CheckIcon from "@mui/icons-material/Check";
import CloseIcon from "@mui/icons-material/Close";
import {
  Avatar,
  Box,
  Button,
  Card,
  Chip,
  CircularProgress,
  Dialog,
  DialogContent,
  DialogTitle,
  Divider,
  IconButton,
  Menu,
  MenuItem,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
import Image from "next/image";
import React, { useState } from "react";

interface MomentCardProps {
  image?: string;
  name: string;
  title?: string;
  desc: string;
  id: number;
  onEdit: (value: MomentDetails) => void;
  onImageUpdate?: (id: number, imageUrl: string) => void;
  components: Component[];
}

interface ComponentDetailsDialogProps {
  component: ComponentMatch | null;
  frameworkId: number | null;
  onClose: () => void;
  open: boolean;
}

const ComponentDetailsDialog: React.FC<ComponentDetailsDialogProps> = ({
  component,
  frameworkId,
  onClose,
  open,
}) => (
  <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
    <DialogTitle
      sx={{
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        mb: 2,
      }}
    >
      Component Details
      <Chip
        label={component?.numbering}
        avatar={<Avatar>{frameworkId === 1 ? "CC" : "BC"}</Avatar>}
        sx={{ ml: 2 }}
      />
    </DialogTitle>
    <DialogContent>
      <Typography variant="subtitle1" gutterBottom>
        Component Type
      </Typography>
      <Typography color="text.secondary" variant="body1" mb={2}>
        {frameworkId === 1 ? "Customer View" : "Business View"}
      </Typography>
      <Typography variant="subtitle1" gutterBottom>
        Component Name
      </Typography>
      <Typography color="text.secondary" variant="body1" mb={2}>
        {`${component?.numbering} ${component?.name}`}
      </Typography>

      <Typography variant="subtitle1" gutterBottom>
        Component Description
      </Typography>
      <Typography color="text.secondary" variant="body1" mb={2}>
        {component?.description}
      </Typography>

      <Box sx={{ display: "flex", justifyContent: "flex-end", gap: 2, mt: 5 }}>
        <Button onClick={onClose}>Close</Button>
      </Box>
    </DialogContent>
  </Dialog>
);

const MomentCard: React.FC<MomentCardProps> = ({
  image,
  name,
  title,
  desc,
  id,
  components,
  onEdit,
  onImageUpdate,
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [matches, setMatches] = useState<FrameworkMatch[]>(
    components.map((component) => ({
      framework_id: component.framework,
      components: [
        {
          numbering: component.numbering,
          id: component.id,
          name: component.name,
          description: component.description,
        },
      ],
    }))
  );
  const [isLoading, setIsLoading] = useState(false);
  const [isImageLoading, setIsImageLoading] = useState(false);
  const [isGeneratingSummary, setIsGeneratingSummary] = useState(false);
  const [generatedImage, setGeneratedImage] = useState<string | undefined>(image);
  const [momentDescription, setMomentDescription] = useState(desc);
  const [isEditing, setIsEditing] = useState(false);
  const [tempDescription, setTempDescription] = useState(desc);
  const [selectedComponent, setSelectedComponent] = useState<ComponentMatch | null>(null);
  const [selectedFrameworkId, setSelectedFrameworkId] = useState<number | null>(null);
  const { state } = useMapCanvas();

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleMatchClick = async (e: React.MouseEvent) => {
    e.stopPropagation();
    handleMenuClose();
    setIsLoading(true);
    try {
      const result = await mapService.postComponentMatch(momentDescription);
      setMatches(result);
      onEdit({
        moment_id: id.toString(),
        components: result.flatMap((framework) =>
          framework.components.map((component) => component.id)
        ),
      } as MomentDetails);
    } catch (error) {
      console.error("Failed to match components:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleGenerateImage = async () => {
    setIsImageLoading(true);
    handleMenuClose();

    try {
      const imageUrl = await mapService.postGenerateImage(momentDescription);
      setGeneratedImage(imageUrl);
      onImageUpdate?.(id, imageUrl);
      onEdit({ moment_id: id.toString(), image_url: imageUrl } as MomentDetails);
    } catch (error) {
      console.error("Failed to generate image:", error);
    } finally {
      setIsImageLoading(false);
    }
  };

  const handleSave = () => {
    setIsEditing(false);
    setMomentDescription(tempDescription);
    onEdit({ moment_id: id.toString(), description: tempDescription } as MomentDetails);
  };

  const handleCancel = () => {
    setIsEditing(false);
    setTempDescription(momentDescription);
  };

  const handleChipClick = (e: React.MouseEvent, component: ComponentMatch, frameworkId: number) => {
    e.stopPropagation();
    setSelectedComponent(component);
    setSelectedFrameworkId(frameworkId);
  };

  const handleGenerateMomentSummary = async (e: React.MouseEvent) => {
    e.stopPropagation();
    handleMenuClose();
    setIsGeneratingSummary(true);

    try {
      // Get all activities for this moment
      const momentActivities = state.moments.find((m) => m.moment_id === id.toString())?.activities;

      // Format the data for the AI service
      const requestData = {
        moment: {
          description: momentDescription || "",
        },
        activities: momentActivities
          ? Object.values(momentActivities)
              .flat()
              .map((activity) => ({
                description: activity.description,
                type: activity.activity_type,
              }))
          : [],
      };

      // Call the AI service
      const generatedSummary = await aiService.generateText("moment", requestData);

      // Update the moment with the generated summary
      setMomentDescription(generatedSummary);
      onEdit({ moment_id: id.toString(), description: generatedSummary } as MomentDetails);
    } catch (error) {
      console.error("Failed to generate moment summary:", error);
    } finally {
      setIsGeneratingSummary(false);
    }
  };

  return (
    <>
      <Card
        sx={{
          bgcolor: "transparent",
          height: "100%",
          width: "100%",
          flexDirection: "column",
          p: 2,
          position: "relative",
          boxSizing: "border-box",
          borderRadius: 0,
        }}
      >
        <Box
          sx={{
            position: "absolute",
            top: 30,
            right: 30,
            zIndex: 1,
            backgroundColor: "rgba(0, 0, 0, 0.8)",
            borderRadius: 1,
            padding: 0.5,
            display: "flex",
            gap: 1,
            flexWrap: "wrap",
            maxWidth: "80%",
          }}
        >
          {isLoading ? (
            <CircularProgress size={24} sx={{ color: "white" }} />
          ) : matches.length > 0 ? (
            matches.flatMap((framework) =>
              framework.components.map((comp) => (
                <Tooltip
                  key={comp.id}
                  placement="top"
                  title={
                    <React.Fragment>
                      {comp.numbering}
                      <br />
                      {comp.name}
                    </React.Fragment>
                  }
                >
                  <Chip
                    onClick={(e) => handleChipClick(e, comp, framework.framework_id)}
                    label={comp.numbering}
                    avatar={
                      <Avatar
                        sx={{
                          color: "white",
                        }}
                      >
                        {framework.framework_id === 1 ? "CC" : "BC"}
                      </Avatar>
                    }
                    sx={{
                      my: 0.7,
                      ml: 0.7,
                      color: "white",
                      backgroundColor: "rgba(255, 255, 255, 0.2)",
                    }}
                  />
                </Tooltip>
              ))
            )
          ) : (
            <IconButton color="primary" onClick={handleMatchClick}>
              <AccountTree fontSize="small" />
            </IconButton>
          )}

          <IconButton color="primary" onClick={handleMenuOpen}>
            <MoreVert fontSize="small" />
          </IconButton>
        </Box>

        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
          onClick={(e) => e.stopPropagation()}
        >
          <MenuItem onClick={handleMatchClick}>Mapping component</MenuItem>
          <MenuItem onClick={handleGenerateMomentSummary} disabled={isGeneratingSummary}>
            Generate moment summary
          </MenuItem>
          <Divider />
          <MenuItem onClick={handleGenerateImage}>Generate image</MenuItem>
          <MenuItem onClick={handleMenuClose}>Delete image</MenuItem>
        </Menu>

        <Box
          sx={{
            position: "relative",
            height: "80%",
            width: "100%",
            backgroundColor: "map.phase",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          {isImageLoading ? (
            <CircularProgress />
          ) : generatedImage ? (
            <Image src={generatedImage} alt={name} fill style={{ objectFit: "cover" }} />
          ) : (
            <Box
              sx={{
                width: "100%",
                height: "100%",
                backgroundColor: "transparent",
              }}
            />
          )}
        </Box>

        <Box
          sx={{
            height: "20%",
            p: 2,
            backgroundColor: "background.paper",
            overflowY: "auto",
            borderRadius: "0 0 8px 8px",
            display: "flex",
            flexDirection: "column",
            justifyContent: "flex-start",
          }}
        >
          {title && (
            <Typography
              variant="subtitle2"
              sx={{
                fontWeight: 600,
                color: "text.primary",
                mb: 1,
                fontSize: "0.875rem",
              }}
            >
              {title}
            </Typography>
          )}
          <Box sx={{ display: "flex", alignItems: "flex-end", gap: 1, flex: 1 }}>
            <TextField
              value={
                isGeneratingSummary
                  ? "Generating moment summary..."
                  : isEditing
                  ? tempDescription
                  : momentDescription
              }
              onChange={(e) => {
                e.stopPropagation();
                setTempDescription(e.target.value);
              }}
              onFocus={(e) => {
                e.stopPropagation();
                setIsEditing(true);
                setTempDescription(momentDescription);
              }}
              multiline
              variant="standard"
              onMouseDown={(e) => e.stopPropagation()}
              onKeyDown={(e) => {
                e.stopPropagation();
                if (e.key === "Enter" && e.ctrlKey) {
                  handleSave();
                } else if (e.key === "Escape") {
                  handleCancel();
                }
              }}
              sx={{
                flexGrow: 1,
                p: 1,
                "& .MuiInputBase-root": {
                  alignItems: "flex-end",
                  cursor: "text",
                },
                "& .MuiInputBase-input": {
                  textAlign: "left",
                  verticalAlign: "bottom",
                  cursor: "text",
                },
              }}
            />
            {isEditing && (
              <Box sx={{ display: "flex", gap: 1, pb: 1 }}>
                <IconButton
                  size="small"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleSave();
                  }}
                  onMouseDown={(e) => e.stopPropagation()}
                  sx={{ color: "success.main" }}
                >
                  <CheckIcon fontSize="small" />
                </IconButton>
                <IconButton
                  size="small"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleCancel();
                  }}
                  onMouseDown={(e) => e.stopPropagation()}
                  sx={{ color: "error.main" }}
                >
                  <CloseIcon fontSize="small" />
                </IconButton>
              </Box>
            )}
          </Box>
        </Box>
      </Card>

      <ComponentDetailsDialog
        component={selectedComponent}
        frameworkId={selectedFrameworkId}
        open={!!selectedComponent}
        onClose={() => setSelectedComponent(null)}
      />
    </>
  );
};

export default MomentCard;
