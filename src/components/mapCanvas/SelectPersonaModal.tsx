import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Typography } from "@mui/material";
import React, { useState } from "react";

interface PersonaData {
  id: number;
  name: string;
  image?: string;
  ageRange?: string;
  gender?: string;
  status?: string;
  location?: string;
  job?: string;
  about?: string;
  goals?: string;
}

interface SelectPersonaModalProps {
  open: boolean;
  onClose: () => void;
  onConfirm?: (persona: PersonaData) => void;
  roleType?: string;
  mapId?: number | string;
}

const SelectPersonaModal: React.FC<SelectPersonaModalProps> = ({
  open,
  onClose,
  onConfirm,
  roleType = "Customer",
}) => {
  const [selectedPersona, setSelectedPersona] = useState<PersonaData | null>(null);

  // Mock personas data for demo
  const mockPersonas: PersonaData[] = [
    {
      id: 1,
      name: "<PERSON>",
      image: "https://via.placeholder.com/80x80?text=JD",
      ageRange: "25-34",
      gender: "Male",
      status: "Active",
      location: "New York",
      job: "Software Engineer",
      about: "Tech-savvy professional",
      goals: "Efficient workflow"
    },
    {
      id: 2,
      name: "<PERSON>",
      image: "https://via.placeholder.com/80x80?text=JS",
      ageRange: "35-44",
      gender: "Female", 
      status: "Active",
      location: "California",
      job: "Product Manager",
      about: "Strategic thinker",
      goals: "User satisfaction"
    }
  ];

  const handlePersonaSelect = (persona: PersonaData) => {
    setSelectedPersona(persona);
  };

  const handleConfirm = () => {
    if (selectedPersona && onConfirm) {
      onConfirm(selectedPersona);
    }
    onClose();
  };

  return (
    <Modal open={open} onClose={onClose} aria-labelledby="select-persona-modal">
      <Box
        sx={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          bgcolor: "background.paper",
          border: "1px solid",
          borderColor: "divider",
          borderRadius: 2,
          width: "90%",
          maxWidth: 1000,
          maxHeight: "90vh",
          overflow: "auto",
          outline: "none",
          p: 0,
        }}
      >
        <Box sx={{ p: 3, borderBottom: 1, borderColor: "divider" }}>
          <Typography variant="h5">Select a persona ({roleType})</Typography>
        </Box>

        <Stack direction={{ xs: "column", md: "row" }} sx={{ height: "100%" }}>
          {/* Left side - Persona selection */}
          <Box
            sx={{
              flex: 7,
              borderRight: "1px solid",
              borderBottom: "1px solid",
              borderColor: "divider",
              p: 3,
            }}
          >
            <Stack spacing={3}>
              <Stack direction="row" flexWrap="wrap" spacing={2} useFlexGap>
                {mockPersonas.map((persona) => (
                  <Box
                    key={persona.id}
                    onClick={() => handlePersonaSelect(persona)}
                    sx={{
                      display: "flex",
                      flexDirection: "column",
                      alignItems: "center",
                      cursor: "pointer",
                      p: 1,
                      borderRadius: 1,
                      width: { xs: "80px", sm: "100px" },
                      bgcolor:
                        selectedPersona?.id === persona.id
                          ? "action.selected"
                          : "transparent",
                      "&:hover": {
                        bgcolor: "action.hover",
                      },
                    }}
                  >
                    <Avatar
                      src={persona.image || ""}
                      alt={persona.name}
                      sx={{ width: 80, height: 80, mb: 1 }}
                    />
                    <Typography align="center" variant="body2" noWrap>
                      {persona.name}
                    </Typography>
                  </Box>
                ))}
              </Stack>
            </Stack>
          </Box>

          {/* Right side - Persona details */}
          <Box sx={{ flex: 5, p: 3 }}>
            {selectedPersona ? (
              <Stack spacing={3}>
                <Typography variant="h6">Profile</Typography>

                <Stack spacing={2}>
                  <Stack direction="row" justifyContent="space-between">
                    <Typography variant="body1" fontWeight="medium">
                      Age:
                    </Typography>
                    <Typography variant="body1">
                      {selectedPersona.ageRange || "Not specified"}
                    </Typography>
                  </Stack>

                  <Stack direction="row" justifyContent="space-between">
                    <Typography variant="body1" fontWeight="medium">
                      Gender:
                    </Typography>
                    <Typography variant="body1">
                      {selectedPersona.gender || "Not specified"}
                    </Typography>
                  </Stack>

                  <Stack direction="row" justifyContent="space-between">
                    <Typography variant="body1" fontWeight="medium">
                      Status:
                    </Typography>
                    <Typography variant="body1">
                      {selectedPersona.status || "Not specified"}
                    </Typography>
                  </Stack>

                  <Stack direction="row" justifyContent="space-between">
                    <Typography variant="body1" fontWeight="medium">
                      Location:
                    </Typography>
                    <Typography variant="body1">
                      {selectedPersona.location || "Not specified"}
                    </Typography>
                  </Stack>

                  <Stack direction="row" justifyContent="space-between">
                    <Typography variant="body1" fontWeight="medium">
                      Job
                    </Typography>
                    <Typography variant="body1">
                      {selectedPersona.job || "Not specified"}
                    </Typography>
                  </Stack>
                </Stack>

                <Typography variant="h6">About</Typography>
                <Typography variant="body2" paragraph>
                  {selectedPersona.about || "No description available."}
                </Typography>

                <Typography variant="h6">Goal</Typography>
                <Typography variant="body2" paragraph>
                  {selectedPersona.goals || "No goals specified."}
                </Typography>
              </Stack>
            ) : (
              <Typography variant="body1" color="text.secondary" align="center" sx={{ mt: 10 }}>
                Select a persona to view details
              </Typography>
            )}
          </Box>
        </Stack>

        <Box
          sx={{
            p: 2,
            borderTop: 1,
            borderColor: "divider",
            display: "flex",
            justifyContent: "flex-end",
          }}
        >
          <Button onClick={onClose} sx={{ mr: 1 }}>
            CANCEL
          </Button>
          <Button variant="contained" onClick={handleConfirm} disabled={!selectedPersona}>
            CONFIRM
          </Button>
        </Box>
      </Box>
    </Modal>
  );
};

export default SelectPersonaModal;