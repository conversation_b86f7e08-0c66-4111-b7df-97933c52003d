import React from 'react';
import { Paper, Typography, Box } from '@mui/material';
import { Moment } from '@/types/api/client';

interface MomentCardProps {
  moment: Moment;
  onClick?: () => void;
  onEdit?: (updates: Partial<Moment>) => void;
  onImageUpdate?: (momentId: string, imageUrl: string) => void;
}

const MomentCard: React.FC<MomentCardProps> = ({ 
  moment,
  onClick,
  onEdit, 
  onImageUpdate 
}) => {
  return (
    <Paper 
      sx={{ 
        p: 2, 
        height: '100%', 
        bgcolor: 'primary.dark',
        border: '1px solid',
        borderColor: 'primary.main',
        display: 'flex',
        flexDirection: 'column',
        cursor: onEdit ? 'pointer' : 'default'
      }}
      onClick={() => {
        if (onClick) onClick();
        if (onEdit) onEdit({ moment_id: moment.moment_id, name: moment.name, description: moment.description });
      }}
    >
      {moment.image_url && (
        <Box
          component="img"
          src={moment.image_url}
          alt={moment.name || 'Moment image'}
          sx={{
            width: '100%',
            height: 120,
            objectFit: 'cover',
            borderRadius: 1,
            mb: 1
          }}
        />
      )}
      
      <Typography variant="subtitle2" color="primary.main">
        Moment
      </Typography>
      <Typography variant="caption" color="primary.light" sx={{ mt: 0.5 }}>
        {moment.name || "Untitled Moment"}
      </Typography>
      
      {moment.description && (
        <Typography variant="caption" color="primary.light" sx={{ mt: 1, opacity: 0.8 }}>
          {moment.description}
        </Typography>
      )}
      
      {moment.components && moment.components.length > 0 && (
        <Typography variant="caption" color="primary.light" sx={{ mt: 1, fontSize: '0.7rem' }}>
          {moment.components.length} component{moment.components.length !== 1 ? 's' : ''}
        </Typography>
      )}

      {moment.sequence && (
        <Typography variant="caption" color="primary.light" sx={{ mt: 1, fontSize: '0.7rem' }}>
          Sequence: {moment.sequence}
        </Typography>
      )}
    </Paper>
  );
};

export default MomentCard;