import React from 'react';
import { Paper, Typography } from '@mui/material';
import { CellProps } from '@/types/grid.types';

export const MapActionCell: React.FC<CellProps> = ({ row, phase, data }) => {
  return (
    <Paper 
      sx={{ 
        p: 2, 
        height: '100%', 
        bgcolor: 'success.dark',
        border: '1px solid',
        borderColor: 'success.main'
      }}
    >
      <Typography variant="subtitle2" color="success.main">
        {row.name}
      </Typography>
      <Typography variant="caption" color="success.light" sx={{ mt: 0.5 }}>
        {row.name} - {phase.name}
      </Typography>
    </Paper>
  );
};