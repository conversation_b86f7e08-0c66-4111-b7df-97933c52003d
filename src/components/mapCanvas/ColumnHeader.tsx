import { Box, Card, Typography } from "@mui/material";
import React from "react";

export interface ColumnHeaderProps {
  /** Moment data */
  moment?: {
    id: string | number;
    name: string;
  };
  /** Phase data (legacy) */
  phase?: {
    id: string | number;
    name: string;
  };
  /** Stage data for context */
  stage: {
    id: string;
    name: string;
  };
  /** Width of the header cell */
  width?: number;
  /** Height of the header cell */
  height?: number;
  /** Callback when moment is edited */
  onEditMoment?: (moment: { id: string | number; name: string }) => void;
  /** Callback when moment is deleted */
  onDeleteMoment?: (id: string | number) => void;
  /** Callback when phase is edited (legacy) */
  onEditPhase?: (phase: { id: string | number; name: string }) => void;
  /** Callback when phase is deleted (legacy) */
  onDeletePhase?: (id: string | number) => void;
  /** Scale factor for responsive sizing */
  scale?: number;
  /** Column index for alternating background colors */
  columnIndex?: number;
}

export const ColumnHeader: React.FC<ColumnHeaderProps> = ({
  stage,
  width = 150,
  height = 50,
}) => {
  // Use map.header background color
  const getBackgroundColor = () => {
    return "map.header";
  };

  return (
    <Card
      sx={{
        width,
        height,
        backgroundColor: getBackgroundColor(),
        p: 1,
        ml: 0.5,
        borderBottom: "4px solid",
        borderColor: "background.paper",
        borderRadius: 0,
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        boxSizing: "border-box",
        position: "relative",
      }}
    >
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          width: "100%",
          textAlign: "center",
        }}
      >
        <Typography
          variant="body2"
          sx={{
            fontWeight: 600,
            color: "text.contrast",
            textAlign: "center",
            lineHeight: 1.2,
            textTransform: "uppercase",
          }}
        >
          {stage.name}
        </Typography>
      </Box>
    </Card>
  );
};

export default ColumnHeader;