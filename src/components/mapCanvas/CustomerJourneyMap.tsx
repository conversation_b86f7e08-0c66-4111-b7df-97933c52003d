import { useMapCanvas } from "@/context/MapCanvasProvider";
import { Activity, Moment as ApiMoment, Phase as ApiPhase, Persona } from "@/types/api/client";
import { RowGroup, VisibleRow } from "@/types/grid.types";
import { BuildingBlock } from "@/types/map";
import {
  <PERSON><PERSON><PERSON>,
  ArrowDownward,
  <PERSON>Forward,
  ArrowUpward,
  VisibilityOff,
} from "@mui/icons-material";
import { Box, CssBaseline, Menu, MenuItem, Typography } from "@mui/material";
import React, { useEffect, useMemo, useState } from "react";
import { CanvasControlToolbar } from "./CanvasControlToolbar";
import FloatingMenu from "./FloatingMenu";
import { GridCell } from "./GridCell";
import { useGridInteractions } from "./hooks/useGridInteractions";
import { useGridState } from "./hooks/useGridState";
import { RowHeader } from "./RowHeader";

// Grid configuration
const CELL_WIDTH = 770;
const STORY_CELL_HEIGHT = 525;
const DEFAULT_CELL_HEIGHT = 350;
const CELL_GAP = 4; // Gap between cells
const ROW_HEADER_WIDTH = 70;
const SUB_ROW_HEADER_WIDTH = 120;
const COL_HEADER_HEIGHT = 80; // Increased for two-level headers

// Data transformation functions - transform API moments to grid columns
const transformApiMomentsToGridColumns = (
  apiPhases: ApiPhase[],
  apiMoments: ApiMoment[]
): {
  moments: ApiMoment[];
  phaseSpans: { phaseId: string; name: string; startIndex: number; span: number }[];
} => {
  if (apiPhases.length === 0 || apiMoments.length === 0) return { moments: [], phaseSpans: [] };

  // Sort phases by sequence
  const sortedPhases = [...apiPhases].sort((a, b) => (a.sequence || 0) - (b.sequence || 0));

  // Create ordered moments array and phase spans
  const orderedMoments: ApiMoment[] = [];
  const phaseSpans: { phaseId: string; name: string; startIndex: number; span: number }[] = [];

  sortedPhases.forEach((phase) => {
    const phaseMoments = apiMoments
      .filter(
        (moment) => Array.isArray(phase.moment_ids) && phase.moment_ids.includes(moment.moment_id)
      )
      .sort((a, b) => (a.sequence || 0) - (b.sequence || 0));

    if (phaseMoments.length > 0) {
      phaseSpans.push({
        phaseId: phase.phase_id,
        name: phase.name,
        startIndex: orderedMoments.length,
        span: phaseMoments.length,
      });

      orderedMoments.push(...phaseMoments);
    }
  });

  return { moments: orderedMoments, phaseSpans };
};

const getRowGroupsWithActivities = (_activities: Activity[], personas: any[]): RowGroup[] => {
  // Create dynamic row groups based on available persona types
  const personaTypes = Array.from(new Set(personas.map((persona) => persona.persona_type)));

  const dynamicRowGroups: RowGroup[] = [
    {
      id: "story",
      name: "Moment",
      type: "story",
      expandable: false,
      subRows: [],
    },
  ];

  // Add persona-based row groups
  personaTypes.forEach((personaType) => {
    if (personaType) {
      const displayName = personaType
        .replace(/_/g, " ")
        .replace(/\b\w/g, (l: string) => l.toUpperCase());
      const cleanId = personaType.toLowerCase().replace(/\s+/g, "");

      dynamicRowGroups.push({
        id: cleanId,
        name: displayName,
        type: "activity",
        expandable: true,
        subRows: [],
      });
    }
  });

  // Add standard building block rows
  // dynamicRowGroups.push(
  //   { id: "painpoints", name: "Painpoints", type: "block", expandable: false, subRows: [] },
  //   { id: "opportunity", name: "Opportunity", type: "block", expandable: false, subRows: [] },
  //   { id: "voc", name: "Voice of Customer", type: "block", expandable: false, subRows: [] },
  //   { id: "context", name: "Operation Context", type: "block", expandable: false, subRows: [] },
  //   { id: "analysis", name: "Analysis", type: "block", expandable: false, subRows: [] }
  // );

  return dynamicRowGroups;
};

interface CustomerJourneyMapData {
  moments: ApiMoment[];
  activities: Activity[];
  personas: Persona[];
  buildingBlocks: BuildingBlock[];
  buildingBlockTypes: any[];
}

interface CustomerJourneyMapProps {
  map?: any; // TODO: Add proper type
  rowGroups?: RowGroup[];
  // New props for direct data passing
  mapData?: CustomerJourneyMapData;
  onDataChange?: (data: Partial<CustomerJourneyMapData>) => void;
}

export const CustomerJourneyMap: React.FC<CustomerJourneyMapProps> = ({
  map,
  rowGroups: propRowGroups,
  mapData,
}) => {
  // Get state from MapCanvasProvider
  const { state } = useMapCanvas();

  // Internal state for map data (when not provided via props or context)
  const [internalMapData, setInternalMapData] = useState<CustomerJourneyMapData>({
    moments: [],
    activities: [],
    personas: [],
    buildingBlocks: [],
    buildingBlockTypes: [],
  });

  // Use MapCanvas state first, then external mapData, then internal state
  const currentMapData = useMemo(() => {
    if (state.moments.length > 0 || state.phases.length > 0) {
      // Transform MapCanvas state to CustomerJourneyMapData format
      const allActivities = state.moments.flatMap((moment) =>
        Object.values(moment.activities).flat()
      );

      return {
        moments: state.moments,
        activities: allActivities,
        personas: state.personas,
        buildingBlocks: [], // TODO: Add building blocks from state
        buildingBlockTypes: [],
      };
    }
    return mapData || internalMapData;
  }, [state, mapData, internalMapData]);

  // Transform API data to grid format - use moments as columns with phase spans
  const { gridMoments, phaseSpans } = useMemo(() => {
    if (state.phases && state.phases.length > 0 && state.moments && state.moments.length > 0) {
      const { moments, phaseSpans } = transformApiMomentsToGridColumns(state.phases, state.moments);
      return { gridMoments: moments, phaseSpans };
    }
    return { gridMoments: [], phaseSpans: [] };
  }, [state.phases, state.moments]);

  const apiRowGroups = useMemo(() => {
    return getRowGroupsWithActivities(
      currentMapData.activities as Activity[],
      Array.isArray(currentMapData.personas) ? currentMapData.personas : []
    );
  }, [currentMapData.activities, currentMapData.personas]);

  const actualRowGroups = useMemo(() => {
    if (currentMapData.moments.length > 0 || currentMapData.activities.length > 0)
      return apiRowGroups;
    if (propRowGroups && propRowGroups.length > 0) return propRowGroups;
    return []; // No default data
  }, [
    apiRowGroups,
    propRowGroups,
    currentMapData.moments.length,
    currentMapData.activities.length,
  ]);
  const {
    scale,
    setScale,
    scrollPos,
    setScrollPos,
    mode,
    setMode,
    expandedRows,
    toggleRow,
    hiddenColumns,
    hiddenRows,
    toggleRowVisibility,
    toggleColumnVisibility,
    isStoryMode,
    setIsStoryMode,
  } = useGridState();

  const [dropdownAnchor, setDropdownAnchor] = useState<null | HTMLElement>(null);
  const [dropdownContext, setDropdownContext] = useState<{ type: string; id: string } | null>(null);
  const [showSubHeaders] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize data from map prop
  useEffect(() => {
    if (map && !isInitialized && !mapData) {
      console.log("Initializing CustomerJourneyMap with map data:", map);
      const newMapData: CustomerJourneyMapData = {
        moments: map.moments || [],
        activities: map.activities || [],
        personas: map.personas || [],
        buildingBlocks: map.building_blocks || [],
        buildingBlockTypes: map.building_block_types || [],
      };
      setInternalMapData(newMapData);
      setIsInitialized(true);
    }
  }, [map, isInitialized, mapData]);

  // Helper function to get cell height based on row type
  const getCellHeight = (rowType: string) => {
    return rowType === "story" ? STORY_CELL_HEIGHT : DEFAULT_CELL_HEIGHT;
  };

  // Calculate visible rows based on expanded state and sub header toggle
  const visibleRows = useMemo(() => {
    const rows: VisibleRow[] = [];
    actualRowGroups.forEach((group) => {
      if (hiddenRows.includes(group.id)) return;

      // For Customer, Front Stage, Back Stage - check if sub headers should be shown
      const hasToggleableSubRows = ["customer", "frontStage", "backStage"].includes(group.id);

      if (
        hasToggleableSubRows &&
        showSubHeaders &&
        group.subRows.length > 0 &&
        expandedRows[group.id]
      ) {
        // Show sub rows when toggle is enabled and group is expanded
        group.subRows.forEach((subRow, index) => {
          if (!hiddenRows.includes(subRow.id)) {
            rows.push({
              ...subRow,
              isFirstSubRow: index === 0,
              isLastSubRow: index === group.subRows.length - 1,
              parentSpan: group.subRows.filter((sr) => !hiddenRows.includes(sr.id)).length,
            });
          }
        });
      } else {
        // Show parent row (always show for non-toggleable groups, or when sub headers are hidden)
        rows.push(group);
      }
    });
    return rows;
  }, [actualRowGroups, hiddenRows, expandedRows, showSubHeaders]);

  // Use moments as columns
  const visibleMoments = useMemo(
    () => gridMoments.filter((_, index) => !hiddenColumns.includes(index.toString())),
    [gridMoments, hiddenColumns]
  );

  const visibleRowIds = useMemo(
    () => actualRowGroups.filter((row) => !hiddenRows.includes(row.id)).map((row) => row.id),
    [actualRowGroups, hiddenRows]
  );

  const totalHeaderWidth = ROW_HEADER_WIDTH + SUB_ROW_HEADER_WIDTH;

  // Grid interactions
  const { scrollAreaRef, handleWheel, handleMouseDown, handleScroll } = useGridInteractions({
    mode,
    scale,
    onScroll: setScrollPos,
    onCellSelect: () => {
      // Disable cell selection
    },
  });

  // Zoom controls
  const zoomIn = () => setScale(Math.min(scale * 1.2, 2));
  const zoomOut = () => setScale(Math.max(scale * 0.8, 0.1));
  const fitToScreen = () => {
    // Reset scale and scroll position, also reset scroll area scroll position
    setScale(0.8);
    setScrollPos({ x: 0, y: 0 });
    // Reset the actual scroll area scroll position
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollLeft = 0;
      scrollAreaRef.current.scrollTop = 0;
    }
  };

  // Dropdown handlers
  const handleDropdownOpen = (event: React.MouseEvent, type: string, id: string) => {
    event.stopPropagation();
    setDropdownAnchor(event.currentTarget as HTMLElement);
    setDropdownContext({ type, id });
  };

  const handleDropdownClose = () => {
    setDropdownAnchor(null);
    setDropdownContext(null);
  };

  const handleDropdownAction = (action: string) => {
    if (dropdownContext) {
      if (action === "hide") {
        if (dropdownContext.type === "column") {
          toggleColumnVisibility(dropdownContext.id);
        } else {
          toggleRowVisibility(dropdownContext.id);
        }
      }
      // Handle other actions like move up/down/left/right
    }
    handleDropdownClose();
  };

  return (
    <>
      <CssBaseline />
      <Box
        sx={{
          width: "100vw",
          height: "100vh",
          bgcolor: "background.default",
          display: "flex",
          flexDirection: "column",
        }}
      >
        {/* FloatingMenu */}
        <FloatingMenu
          name={state.name || map?.name || "Customer Journey Map"}
          mapId={state.map_id || map?.id || ""}
          back={() => window.history.back()}
          status={state.state || map?.status || "Draft"}
        />

        {/* Grid Container */}
        <Box
          sx={{
            flex: 1,
            position: "relative",
            overflow: "hidden",
            pt: "140px",
            pl: "calc(var(--sidebar-width, 240px) + 200px)",
            pr: "200px",
            pb: "15px",
          }}
        >
          {/* Canvas Control Toolbar */}
          <CanvasControlToolbar
            scale={scale}
            mode={mode}
            isStoryMode={isStoryMode}
            visibleRows={visibleRowIds}
            onModeChange={setMode}
            onZoomIn={zoomIn}
            onZoomOut={zoomOut}
            onFitToScreen={fitToScreen}
            onToggleStoryMode={() => setIsStoryMode(!isStoryMode)}
            onRowToggle={toggleRowVisibility}
          />

          {/* Fixed top-left corner */}
          <Box
            sx={{
              position: "absolute",
              top: "85px",
              left: "120px",
              width: totalHeaderWidth * scale,
              height: COL_HEADER_HEIGHT * scale,
              zIndex: 10,
              overflow: "hidden",
            }}
          >
            <Box
              sx={{
                transform: `scale(${scale})`,
                transformOrigin: "top left",
                width: totalHeaderWidth,
                height: COL_HEADER_HEIGHT,
                bgcolor: "map.header",
                borderBottom: "4px solid",
                borderColor: "background.paper",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <Typography variant="body2" sx={{ fontWeight: 600, color: "text.contrast" }}>
                PHASES
              </Typography>
            </Box>
          </Box>

          {/* Fixed column headers */}
          <Box
            sx={{
              position: "absolute",
              top: "85px",
              left: `calc(${totalHeaderWidth * scale}px + 120px)`,
              right: 0,
              height: COL_HEADER_HEIGHT * scale,
              zIndex: 9,
              overflow: "hidden",
            }}
          >
            <Box
              sx={{
                transform: `translateX(-${scrollPos.x}px)`,
                width: visibleMoments.length * CELL_WIDTH * scale,
                height: "100%",
              }}
            >
              <Box
                sx={{
                  transform: `scale(${scale})`,
                  transformOrigin: "top left",
                  display: "flex",
                  flexDirection: "column",
                  height: COL_HEADER_HEIGHT,
                  width: visibleMoments.length * CELL_WIDTH,
                }}
              >
                {/* Phase headers */}
                <Box sx={{ display: "flex", height: COL_HEADER_HEIGHT }}>
                  {phaseSpans.map((phaseSpan) => (
                    <Box
                      key={phaseSpan.phaseId}
                      sx={{
                        width: CELL_WIDTH * phaseSpan.span,
                        height: "100%",
                        bgcolor: "map.header",
                        borderLeft: "5px solid",
                        borderBottom: "4px solid",
                        borderColor: "background.paper",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                      }}
                    >
                      <Typography
                        variant="body2"
                        sx={{
                          fontWeight: 700,
                          color: "text.contrast",
                          textAlign: "center",
                          textTransform: "uppercase",
                        }}
                      >
                        {phaseSpan.name}
                      </Typography>
                    </Box>
                  ))}
                </Box>
              </Box>
            </Box>
          </Box>

          {/* Fixed row headers */}
          <Box
            sx={{
              position: "absolute",
              top: `calc(${COL_HEADER_HEIGHT * scale}px + 85px)`,
              left: "120px",
              bottom: 0,
              width: totalHeaderWidth * scale,
              zIndex: 9,
              overflow: "hidden",
            }}
          >
            <Box
              sx={{
                width: totalHeaderWidth * scale,
                height:
                  visibleRows.reduce(
                    (total, row) => total + getCellHeight(row.type) + CELL_GAP,
                    0
                  ) * scale,
              }}
            >
              <Box
                sx={{
                  transform: `translateY(-${scrollPos.y}px) scale(${scale})`,
                  transformOrigin: "top left",
                  width: totalHeaderWidth,
                  height: visibleRows.reduce(
                    (total, row) => total + getCellHeight(row.type) + CELL_GAP,
                    0
                  ),
                  display: "flex",
                  flexDirection: "column",
                  gap: `${CELL_GAP}px`,
                }}
              >
                {visibleRows.map((row) => {
                  const isParentRow = !row.parentId;
                  const parentGroup = row.parentId
                    ? actualRowGroups.find((g) => g.id === row.parentId)
                    : null;
                  const hasSubRows = isParentRow
                    ? row.subRows?.length > 0
                    : parentGroup?.subRows?.length > 0;

                  return (
                    <RowHeader
                      key={row.id}
                      row={row}
                      parentGroup={parentGroup}
                      isFirstSubRow={row.isFirstSubRow}
                      parentSpan={row.parentSpan}
                      hasSubRows={hasSubRows || false}
                      isExpanded={expandedRows[isParentRow ? row.id : parentGroup?.id || ""]}
                      cellHeight={getCellHeight(row.type)}
                      totalHeaderWidth={totalHeaderWidth}
                      showSubHeaders={showSubHeaders}
                      onToggle={
                        hasSubRows
                          ? () => toggleRow(isParentRow ? row.id : parentGroup?.id || "")
                          : undefined
                      }
                      onMenuClick={(e, rowId) => handleDropdownOpen(e, "row", rowId)}
                    />
                  );
                })}
              </Box>
            </Box>
          </Box>

          {/* Scrollable content area */}
          <Box
            ref={scrollAreaRef}
            sx={{
              position: "absolute",
              top: `calc(${COL_HEADER_HEIGHT * scale}px + 85px)`,
              left: `calc(${totalHeaderWidth * scale}px + 120px)`,
              right: 0,
              bottom: 0,
              overflow: "auto",
              cursor: mode === "pan" ? "grab" : "default",
              "&:active": {
                cursor: mode === "pan" ? "grabbing" : "default",
              },
            }}
            onWheel={(e) => handleWheel(e.nativeEvent)}
            onMouseDown={handleMouseDown}
            onScroll={handleScroll}
          >
            <Box
              sx={{
                width: visibleMoments.length * CELL_WIDTH * scale + 100,
                height:
                  visibleRows.reduce(
                    (total, row) => total + getCellHeight(row.type) + CELL_GAP,
                    0
                  ) *
                    scale +
                  100,
              }}
            >
              <Box
                sx={{
                  transform: `scale(${scale})`,
                  transformOrigin: "top left",
                  width: visibleMoments.length * CELL_WIDTH,
                  height: visibleRows.reduce(
                    (total, row) => total + getCellHeight(row.type) + CELL_GAP,
                    0
                  ),
                  display: "flex",
                  flexDirection: "column",
                  gap: `${CELL_GAP}px`,
                }}
              >
                {visibleRows.map((row) => (
                  <Box
                    key={row.id}
                    sx={{
                      display: "flex",
                    }}
                  >
                    {visibleMoments.map((moment, columnIndex) => {
                      return (
                        <GridCell
                          key={`${row.id}-${moment.moment_id}`}
                          row={row}
                          moment={moment}
                          isSelected={false}
                          cellWidth={CELL_WIDTH}
                          cellHeight={getCellHeight(row.type)}
                          columnIndex={columnIndex}
                        />
                      );
                    })}
                  </Box>
                ))}
              </Box>
            </Box>
          </Box>

          {/* Dropdown Menu */}
          <Menu
            anchorEl={dropdownAnchor}
            open={Boolean(dropdownAnchor)}
            onClose={handleDropdownClose}
            anchorOrigin={{ vertical: "bottom", horizontal: "left" }}
          >
            {dropdownContext?.type === "column" ? (
              <>
                <MenuItem onClick={() => handleDropdownAction("moveLeft")}>
                  <ArrowBack fontSize="small" sx={{ mr: 1 }} />
                  Move Left
                </MenuItem>
                <MenuItem onClick={() => handleDropdownAction("moveRight")}>
                  <ArrowForward fontSize="small" sx={{ mr: 1 }} />
                  Move Right
                </MenuItem>
              </>
            ) : (
              <>
                <MenuItem onClick={() => handleDropdownAction("moveUp")}>
                  <ArrowUpward fontSize="small" sx={{ mr: 1 }} />
                  Move Up
                </MenuItem>
                <MenuItem onClick={() => handleDropdownAction("moveDown")}>
                  <ArrowDownward fontSize="small" sx={{ mr: 1 }} />
                  Move Down
                </MenuItem>
              </>
            )}
            <MenuItem onClick={() => handleDropdownAction("hide")}>
              <VisibilityOff fontSize="small" sx={{ mr: 1 }} />
              Hide
            </MenuItem>
          </Menu>
        </Box>
      </Box>
    </>
  );
};
