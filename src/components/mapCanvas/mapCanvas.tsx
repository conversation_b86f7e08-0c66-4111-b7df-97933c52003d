import CanvasControlToolbar from "@/components/mapCanvas/CanvasControlToolbar";
import { useAppContext } from "@/context/AppProvider";
import { MapCanvasActionType, useMapCanvas } from "@/context/MapCanvasProvider";
import {
  processActionsByTypeRole,
  processBuildingBlocksByType,
  processToggleMenuBuildingBlockTypes,
} from "@/utils/mapHelper";
import AddCircleIcon from "@mui/icons-material/AddCircle";
import { Box, Card, IconButton, Stack, Typography } from "@mui/material";
import { useCallback, useEffect, useRef } from "react";
import { TransformComponent, TransformWrapper } from "react-zoom-pan-pinch";
import FloatingMenu from "./FloatingMenu";
import { useActionHandlers } from "./hooks/useActionHandlers";
import { useBuildingBlockHandlers } from "./hooks/useBuildingBlockHandlers";
import { useCanvas } from "./hooks/useCanvas";
import { usePhaseActions } from "./hooks/usePhaseActions";
import { useUIStateHandlers } from "./hooks/useUIStateHandlers";
import MapPhaseColumn from "./MapPhaseColumn";
import MapRowHeader from "./MapRowHeader";
import SelectPersonaModal from "./SelectPersonaModal";

// Internal component to access zoom controls
const MapCanvasContent = ({
  map,
  zoomIn,
  zoomOut,
  resetTransform,
  isPanningEnabled,
  setIsPanningEnabled,
  isSpacePressed,
  onReset,
}) => {
  const { state, dispatch } = useMapCanvas();
  const { commonData } = useAppContext();

  // Canvas hooks (includes map interaction)
  const { mapInteractionValue, contentRef, containerRef, setupResizeObserver, getContainerStyles } =
    useCanvas({
      onZoomChange: useCallback((scale: number) => {
        // This callback will be triggered when zoom changes
        // We can use it to update any zoom-dependent calculations
      }, [])
    });

  // Calculate dynamic canvas size based on zoom level
  const baseWidth = state.isStoryMode ? 4000 : 1200;
  const baseHeight = 1200;
  const dynamicWidth = baseWidth * mapInteractionValue.scale;
  const dynamicHeight = baseHeight * mapInteractionValue.scale;

  // Update bounds when content changes
  useEffect(() => {
    return setupResizeObserver();
  }, [state.phases.length, state.isStoryMode, setupResizeObserver]);

  // Update container overflow styles when zoom changes
  useEffect(() => {
    if (containerRef.current) {
      const styles = getContainerStyles();
      Object.assign(containerRef.current.style, styles);
    }
  }, [mapInteractionValue.scale, getContainerStyles]);

  // Handle wheel events to enable normal scrolling
  useEffect(() => {
    const handleWheel = (e: WheelEvent) => {
      // Only handle wheel events that occur within our container
      const container = containerRef.current;
      if (container && container.contains(e.target as Node)) {
        e.preventDefault();
        e.stopPropagation();

        // Manually handle scrolling on the container
        container.scrollTop += e.deltaY;
        container.scrollLeft += e.deltaX;
      }
    };

    // Add wheel event listener to capture wheel events before TransformWrapper
    document.addEventListener("wheel", handleWheel, { passive: false, capture: true });

    return () => {
      document.removeEventListener("wheel", handleWheel, { capture: true } as any);
    };
  }, [containerRef]);

  // Phase-related actions
  const {
    onEditPhase,
    onDeletePhase,
    onAddPhase,
    initializedPhases,
    setInitializedPhases,
    initializationRef,
  } = usePhaseActions({ dispatch, mapId: map.id });

  // Action-related handlers
  const {
    onEditAction,
    onDeleteAction,
    onAddAction,
    handleCreateAction,
    selectedRoleType,
    selectPersonaModalOpen,
    handleClosePersonaModal,
    pendingActionData,
  } = useActionHandlers({ dispatch, mapId: map.id });

  // Process data for rendering
  const actionsByTypeRole = processActionsByTypeRole(
    state.actions,
    state.roles,
    commonData.role_types
  );

  const buildingBlocksByType = processBuildingBlocksByType(
    state.phases,
    state.buildingBlocks,
    commonData.building_block_types
  );

  // UI state handlers
  const {
    handleBack,
    handleStoryModeToggle,
    handleRoleTypeToggle,
    handleBuildingBlockTypeToggle,
    filteredActionsByTypeRole,
    filteredBuildingBlocksByType,
  } = useUIStateHandlers({
    dispatch,
    state,
    actionsByTypeRole,
    buildingBlocksByType,
  });

  // Initialize phases if needed
  useEffect(() => {
    const initializePhases = () => {
      if (!initializationRef.current && map && map.phases.length === 0) {
        initializationRef.current = true;
        onAddPhase();
        onAddPhase();
        setInitializedPhases(true);
      } else if (map) {
        setInitializedPhases(true);
      }
      dispatch({
        type: MapCanvasActionType.SET_DATA,
        payload: {
          ...map,
          id: map.id,
        },
      });
    };

    if (!initializedPhases) {
      initializePhases();
    }
  }, [map, dispatch, initializedPhases, onAddPhase, initializationRef, setInitializedPhases]);

  // Set visible role types
  useEffect(() => {
    dispatch({
      type: MapCanvasActionType.SET_VISIBLE_ROLE_TYPES,
      payload: commonData.role_types.map((role) => role.name),
    });
  }, [commonData.role_types, dispatch]);

  // Set visible building block types
  useEffect(() => {
    const allBuildingBlockTypes = processToggleMenuBuildingBlockTypes(
      commonData.building_block_types
    );
    const allTypeNames = allBuildingBlockTypes.map((type) => type.name);
    dispatch({
      type: MapCanvasActionType.SET_VISIBLE_BUILDING_BLOCK_TYPES,
      payload: allTypeNames,
    });
  }, [commonData.building_block_types, dispatch]);

  // Handle persona selection for creating a new action
  const handlePersonaSelect = (persona: any) => {
    if (pendingActionData) {
      const actionType =
        typeof pendingActionData.type === "string" ? pendingActionData.type : "Application";
      handleCreateAction(pendingActionData.phase, persona.id, actionType);
    }
    handleClosePersonaModal();
  };

  // Building block handlers
  const { handleViewExistingData, handleBuildingBlockCreated } = useBuildingBlockHandlers({
    dispatch,
    mapId: map.id,
    buildingBlocks: state.buildingBlocks,
  });

  return (
    <div ref={containerRef} style={{ width: "100%", height: "100%" }}>
      <FloatingMenu
        name={map.name}
        mapId={map.id}
        status={
          commonData.map_states.find((state) => state.value === map.state)?.label || map.state
        }
        back={handleBack}
      />
      <CanvasControlToolbar
        onReset={onReset || (() => resetTransform())}
        onZoomIn={() => zoomIn()}
        onZoomOut={() => zoomOut()}
        onTogglePanning={() => setIsPanningEnabled(!isPanningEnabled)}
        isPanningDisabled={!isPanningEnabled && !isSpacePressed}
        toggleStoryMode={handleStoryModeToggle}
        isStoryModeOn={state.isStoryMode}
        roleTypes={commonData.role_types}
        visibleRoles={state.visibleRoleTypes}
        onRoleToggle={handleRoleTypeToggle}
        buildingBlockTypes={processToggleMenuBuildingBlockTypes(commonData.building_block_types)}
        visibleBuildingBlockTypes={state.visibleBuildingBlockTypes}
        onBuildingBlockTypeToggle={handleBuildingBlockTypeToggle}
      />

      {/* Visual indicator for panning/pinch mode */}
      {(isPanningEnabled || isSpacePressed) && (
        <Box
          sx={{
            position: "fixed",
            top: 16,
            right: 16,
            backgroundColor: "primary.main",
            color: "white",
            px: 2,
            py: 1,
            borderRadius: 1,
            fontSize: "14px",
            fontWeight: "medium",
            zIndex: 1000,
            boxShadow: 2,
          }}
        >
          {isSpacePressed ? "Pan/Pinch Mode (Space)" : "Pan/Pinch Mode (Toggle)"}
        </Box>
      )}

      <TransformComponent>
        <div ref={contentRef}>
          <Stack
            direction="row"
            spacing={4}
            sx={{
              padding: 4,
              paddingTop: 8, // Extra padding to avoid FloatingMenu overlap
              paddingLeft: 6, // Extra padding to avoid CanvasControlToolbar overlap
              minWidth: `${dynamicWidth}px`,
              minHeight: `${dynamicHeight}px`,
              width: "max-content",
              height: "max-content",
            }}
          >
            {!state.isStoryMode && (
              <Box sx={{ width: 250, flexShrink: 0 }}>
                <Card sx={{ backgroundColor: "map.header", padding: 2 }}>
                  <Typography variant="h6" align="center">
                    Phases
                  </Typography>
                </Card>
                <Card
                  sx={{
                    backgroundColor: "map.header",
                    padding: 2,
                    height: "600px",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    marginTop: 2,
                  }}
                >
                  <Typography variant="h6" align="center">
                    Story
                  </Typography>
                </Card>
                {Object.entries(filteredActionsByTypeRole).map(
                  ([typeRoleKey, { type, role, minHeight: maxHeight }]) => (
                    <MapRowHeader
                      key={typeRoleKey}
                      type={type}
                      role={role}
                      roles={state.roles}
                      height={maxHeight}
                    />
                  )
                )}
                {Object.entries(filteredBuildingBlocksByType)
                  .sort(([, entryA], [, entryB]) => {
                    const orderMap: Record<string, number> = {
                      Painpoints: 1,
                      Opportunity: 2,
                      "Voice of customer": 3,
                      "Operation context": 4,
                      Analytics: 5,
                    };
                    const orderA = orderMap[entryA.typeName] || 999;
                    const orderB = orderMap[entryB.typeName] || 999;
                    return orderA - orderB;
                  })
                  .map(([typeRoleKey, { typeName, minHeight: maxHeight }]) => (
                    <MapRowHeader key={typeRoleKey} type={typeName} height={maxHeight} />
                  ))}
              </Box>
            )}

            {/* Phase Columns */}
            <Stack direction="row" style={{ marginLeft: 0 }}>
              <Box sx={{ display: "flex", flexGrow: 1 }}>
                <Stack
                  direction="row"
                  spacing={2}
                  flexWrap={state.isStoryMode ? "wrap" : "nowrap"}
                  width={state.isStoryMode ? "4000px" : "auto"}
                >
                  {state.phases.map((phase) => (
                    <MapPhaseColumn
                      key={`phase-${phase.id}`}
                      phase={phase}
                      actionsByTypeRole={filteredActionsByTypeRole}
                      buildingBlocksByType={filteredBuildingBlocksByType}
                      onEditPhase={
                        phase.id.toString().startsWith("empty-") ? undefined : onEditPhase
                      }
                      onDeletePhase={
                        phase.id.toString().startsWith("empty-") ? undefined : onDeletePhase
                      }
                      onEditAction={onEditAction}
                      onDeleteAction={onDeleteAction}
                      onAddAction={onAddAction}
                      onViewExistingData={handleViewExistingData}
                      onBuildingBlockCreated={handleBuildingBlockCreated}
                      isPanningDisabled={false} // Always allow interactions
                      isCompactView={state.isStoryMode}
                      isEmpty={phase.id.toString().startsWith("empty-")}
                      mapId={map.id}
                      transformValues={{
                        scale: mapInteractionValue.scale,
                        positionX: mapInteractionValue.translation.x,
                        positionY: mapInteractionValue.translation.y,
                      }}
                    />
                  ))}
                </Stack>
              </Box>
              {!state.isStoryMode && (
                <Box sx={{ display: "flex", alignItems: "start", mt: 5, flexShrink: 0 }}>
                  <IconButton color="primary" onClick={onAddPhase}>
                    <AddCircleIcon fontSize="large" />
                  </IconButton>
                </Box>
              )}
            </Stack>
          </Stack>
        </div>
      </TransformComponent>

      <SelectPersonaModal
        open={selectPersonaModalOpen}
        onClose={handleClosePersonaModal}
        onConfirm={handlePersonaSelect}
        roleType={selectedRoleType}
        mapId={map.id}
      />
    </div>
  );
};

// Main MapCanvas component with custom zoom functionality
const MapCanvas = ({ map }) => {
  const { state } = useMapCanvas();
  const {
    contentBounds,
    handleTransformChange,
    isSpacePressed,
    isPanningEnabled,
    setIsPanningEnabled,
    createZoomInFunction,
    createZoomOutFunction,
    getContainerStyles,
    mapInteractionValue,
  } = useCanvas();

  // Calculate if pinch should be enabled
  const shouldEnablePinch = isPanningEnabled || isSpacePressed;
  const shouldEnablePanning = isPanningEnabled || isSpacePressed;

  // Ref for the main container to apply dynamic styles
  const mainContainerRef = useRef<HTMLDivElement>(null);

  // Apply dynamic overflow styles when zoom changes
  useEffect(() => {
    if (mainContainerRef.current) {
      const currentDynamicWidth = (state.isStoryMode ? 4000 : 1200) * mapInteractionValue.scale;
      const currentDynamicHeight = 1200 * mapInteractionValue.scale;
      const styles = getContainerStyles(mainContainerRef.current, currentDynamicWidth, currentDynamicHeight);
      Object.assign(mainContainerRef.current.style, styles);
    }
  }, [getContainerStyles, mapInteractionValue.scale, state.isStoryMode]);

  // Also update on transform change
  const handleTransformChangeWithScrollUpdate = useCallback(
    (ref: any, transformState: any) => {
      handleTransformChange(ref, transformState);
      // Update scroll styles after transform
      if (mainContainerRef.current) {
        setTimeout(() => {
          const newDynamicWidth = (state.isStoryMode ? 4000 : 1200) * transformState.scale;
          const newDynamicHeight = 1200 * transformState.scale;
          const styles = getContainerStyles(mainContainerRef.current!, newDynamicWidth, newDynamicHeight);
          Object.assign(mainContainerRef.current!.style, styles);
        }, 0);
      }
    },
    [handleTransformChange, getContainerStyles, state.isStoryMode]
  );

  return (
    <Box
      ref={mainContainerRef}
      sx={{
        width: "100%",
        height: "100vh", // Set explicit height for scrolling
        overflow: "auto", // Default overflow, will be overridden by dynamic styles
        cursor: shouldEnablePanning ? "grab" : "default",
        // Custom scrollbars
        "& ::-webkit-scrollbar": {
          width: "8px",
          height: "8px",
        },
        "& ::-webkit-scrollbar-track": {
          background: "#f1f1f1",
          borderRadius: "4px",
        },
        "& ::-webkit-scrollbar-thumb": {
          background: "#888",
          borderRadius: "4px",
        },
        "& ::-webkit-scrollbar-thumb:hover": {
          background: "#555",
        },
        // Firefox scrollbars
        scrollbarWidth: "thin",
        scrollbarColor: "#888 #f1f1f1",
      }}
    >
      <TransformWrapper
        initialScale={0.5}
        initialPositionX={0}
        initialPositionY={0}
        minScale={contentBounds.minScale}
        maxScale={contentBounds.maxScale}
        limitToBounds={true}
        centerOnInit={false}
        onTransformed={handleTransformChangeWithScrollUpdate}
        doubleClick={{ disabled: true }}
        panning={{
          disabled: !shouldEnablePanning,
          velocityDisabled: true,
        }}
        pinch={{
          disabled: !shouldEnablePinch,
        }}
        wheel={{ disabled: true }}
        alignmentAnimation={{ disabled: true }}
        velocityAnimation={{ disabled: true }}
      >
        {({ resetTransform, setTransform }) => {
          // Create the actual zoom functions that will be passed to toolbar
          const customZoomIn = createZoomInFunction(setTransform);
          const customZoomOut = createZoomOutFunction(setTransform);

          return (
            <MapCanvasContent
              map={map}
              zoomIn={customZoomIn}
              zoomOut={customZoomOut}
              resetTransform={resetTransform}
              isPanningEnabled={isPanningEnabled}
              setIsPanningEnabled={setIsPanningEnabled}
              isSpacePressed={isSpacePressed}
              onReset={() => resetTransform()}
            />
          );
        }}
      </TransformWrapper>
    </Box>
  );
};

export default MapCanvas;
