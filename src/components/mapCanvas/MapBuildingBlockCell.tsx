import React from 'react';
import { Paper, Typography } from '@mui/material';
import { CellProps } from '@/types/grid.types';

export const MapBuildingBlockCell: React.FC<CellProps> = ({ row, phase, data }) => {
  return (
    <Paper 
      sx={{ 
        p: 2, 
        height: '100%', 
        bgcolor: 'secondary.dark',
        border: '1px solid',
        borderColor: 'secondary.main'
      }}
    >
      <Typography variant="subtitle2" color="secondary.main">
        {row?.name || "Row"}
      </Typography>
      <Typography variant="caption" color="secondary.light" sx={{ mt: 0.5 }}>
        {row?.name || "Row"} - {phase?.name || "Phase"}
      </Typography>
    </Paper>
  );
};
