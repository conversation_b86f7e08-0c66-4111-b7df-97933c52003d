import React from 'react';
import { Paper, Typography } from '@mui/material';
import { VisibleRow, Phase } from '@/types/grid.types';

interface SystemCellProps {
  row: VisibleRow;
  phase: Phase;
}

export const SystemCell: React.FC<SystemCellProps> = ({ row, phase }) => {
  return (
    <Paper 
      sx={{ 
        p: 2, 
        height: '100%', 
        bgcolor: 'grey.900',
        border: '1px solid',
        borderColor: 'grey.700'
      }}
    >
      <Typography variant="subtitle2" color="grey.300">
        {row?.name || "System"}
      </Typography>
      <Typography variant="caption" color="grey.500" sx={{ mt: 0.5 }}>
        {row?.name || "System"} - {phase?.name || "Moment"}
      </Typography>
    </Paper>
  );
};
