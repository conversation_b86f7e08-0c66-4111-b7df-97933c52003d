import React, { useState } from 'react';
import { 
  Box, 
  IconButton, 
  Menu, 
  MenuItem, 
  Switch, 
  Typography,
  Divider,
  Tooltip
} from '@mui/material';
import {
  <PERSON>ropFree,
  PanTool,
  ZoomIn,
  ZoomOut,
  Tune,
  ChevronRight
} from '@mui/icons-material';

interface CanvasControlToolbarProps {
  scale: number;
  mode: 'pan' | 'select';
  isStoryMode: boolean;
  visibleRows: string[];
  onModeChange: (mode: 'pan' | 'select') => void;
  onZoomIn: () => void;
  onZoomOut: () => void;
  onFitToScreen: () => void;
  onToggleStoryMode: () => void;
  onRowToggle: (rowId: string) => void;
}

export const CanvasControlToolbar: React.FC<CanvasControlToolbarProps> = ({
  scale,
  mode,
  isStoryMode,
  visibleRows,
  onModeChange,
  onZoomIn,
  onZoomOut,
  onFitToScreen,
  onToggleStoryMode,
  onRowToggle
}) => {
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);
  const [submenu, setSubmenu] = useState<string | null>(null);

  const actionRows = [
    { id: 'customer', name: 'Customer' },
    { id: 'frontStage', name: 'Front Stage' },
    { id: 'backStage', name: 'Back Stage' }
  ];

  const buildingBlockRows = [
    { id: 'painpoints', name: 'Painpoints' },
    { id: 'opportunity', name: 'Opportunity' },
    { id: 'voc', name: 'Voice of Customer' },
    { id: 'context', name: 'Operation Context' },
    { id: 'analysis', name: 'Analysis' }
  ];

  return (
    <>
      <Box
        sx={{
          position: 'fixed',
          left: 32,
          top: 128,
          zIndex: 1300,
          display: 'flex',
          flexDirection: 'column',
          gap: 0.5,
          bgcolor: 'background.paper',
          border: '1px solid',
          borderColor: 'grey.800',
          borderRadius: 2,
          p: 0.5,
        }}
      >
        <Tooltip title="Fit to Screen" placement="right">
          <IconButton onClick={onFitToScreen} size="small">
            <CropFree />
          </IconButton>
        </Tooltip>

        <Tooltip title={mode === 'pan' ? 'Pan Mode' : 'Select Mode'} placement="right">
          <IconButton 
            onClick={() => onModeChange(mode === 'pan' ? 'select' : 'pan')}
            size="small"
            color={mode === 'pan' ? 'primary' : 'default'}
          >
            <PanTool />
          </IconButton>
        </Tooltip>

        <Tooltip title="Zoom In" placement="right">
          <IconButton 
            onClick={onZoomIn} 
            size="small"
            disabled={scale >= 2}
          >
            <ZoomIn />
          </IconButton>
        </Tooltip>

        <Tooltip title="Zoom Out" placement="right">
          <IconButton 
            onClick={onZoomOut} 
            size="small"
            disabled={scale <= 0.5}
          >
            <ZoomOut />
          </IconButton>
        </Tooltip>

        <Tooltip title="Settings" placement="right">
          <IconButton 
            onClick={(e) => setMenuAnchor(e.currentTarget)}
            size="small"
          >
            <Tune />
          </IconButton>
        </Tooltip>
      </Box>

      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={() => {
          setMenuAnchor(null);
          setSubmenu(null);
        }}
        anchorOrigin={{ vertical: 'center', horizontal: 'right' }}
        transformOrigin={{ vertical: 'center', horizontal: 'left' }}
      >
        <MenuItem sx={{ display: 'flex', justifyContent: 'space-between' }}>
          <Typography variant="body2">Story Mode</Typography>
          <Switch
            size="small"
            checked={isStoryMode}
            onChange={onToggleStoryMode}
            onClick={(e) => e.stopPropagation()}
          />
        </MenuItem>
        
        <Divider />
        
        <MenuItem 
          onClick={() => setSubmenu(submenu === 'actions' ? null : 'actions')}
          sx={{ display: 'flex', justifyContent: 'space-between' }}
        >
          <Typography variant="body2">Action swim lane</Typography>
          <ChevronRight />
        </MenuItem>
        
        <MenuItem 
          onClick={() => setSubmenu(submenu === 'blocks' ? null : 'blocks')}
          sx={{ display: 'flex', justifyContent: 'space-between' }}
        >
          <Typography variant="body2">Building blocks</Typography>
          <ChevronRight />
        </MenuItem>
      </Menu>

      {/* Submenus */}
      {submenu === 'actions' && (
        <Menu
          open={true}
          anchorEl={menuAnchor}
          onClose={() => setSubmenu(null)}
          anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
          transformOrigin={{ vertical: 'top', horizontal: 'left' }}
          sx={{ ml: 2 }}
        >
          {actionRows.map(row => (
            <MenuItem key={row.id} sx={{ display: 'flex', justifyContent: 'space-between' }}>
              <Typography variant="body2">{row.name}</Typography>
              <Switch
                size="small"
                checked={!visibleRows.includes(row.id)}
                onChange={() => onRowToggle(row.id)}
                onClick={(e) => e.stopPropagation()}
              />
            </MenuItem>
          ))}
        </Menu>
      )}

      {submenu === 'blocks' && (
        <Menu
          open={true}
          anchorEl={menuAnchor}
          onClose={() => setSubmenu(null)}
          anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
          transformOrigin={{ vertical: 'top', horizontal: 'left' }}
          sx={{ ml: 2 }}
        >
          {buildingBlockRows.map(row => (
            <MenuItem key={row.id} sx={{ display: 'flex', justifyContent: 'space-between' }}>
              <Typography variant="body2">{row.name}</Typography>
              <Switch
                size="small"
                checked={!visibleRows.includes(row.id)}
                onChange={() => onRowToggle(row.id)}
                onClick={(e) => e.stopPropagation()}
              />
            </MenuItem>
          ))}
        </Menu>
      )}
    </>
  );
};
