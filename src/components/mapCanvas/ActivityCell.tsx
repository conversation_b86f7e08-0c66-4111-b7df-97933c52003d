import { Activity } from "@/types/api/client";
import { useDroppable } from "@dnd-kit/core";
import AddIcon from "@mui/icons-material/Add";
import { <PERSON>, But<PERSON>, Card } from "@mui/material";
import React, { useState } from "react";
import ActivityCard from "./ActivityCard";

type ActivityCellProps = {
  momentId: number | string;
  activityType: string;
  role: string;
  activities: Activity[];
  isPanningDisabled?: boolean;
  onEditActivity: (id: string, updatedText: string) => void;
  onDeleteActivity: (id: string) => void;
  onAddActivity: () => void;
};

export const ActivityCell: React.FC<ActivityCellProps> = ({
  momentId,
  activityType,
  role,
  activities,
  onEditActivity,
  onDeleteActivity,
  onAddActivity,
}) => {
  const [isHovering, setIsHovering] = useState(false);
  const cellId = `cell-${momentId}-${activityType}-${role}`;

  const { setNodeRef, isOver } = useDroppable({
    id: cellId,
    data: {
      type: "activity",
    },
    disabled: false,
  });

  const handleMouseEnter = () => {
    setIsHovering(true);
  };

  const handleMouseLeave = () => {
    setIsHovering(false);
  };

  return (
    <Card
      ref={setNodeRef}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      sx={{
        width: "100%",
        height: "100%",
        p: 2,
        bgcolor: isOver ? "action.hover" : "transparent",
        display: "grid",
        gridTemplateColumns: "repeat(3, 1fr)",
        gap: "1rem",
        position: "relative",
        transition: "background-color 0.2s",
        overflow: "auto",
        borderRadius: 0,
      }}
    >
      {activities.map((activity) => {
        return (
          <ActivityCard
            key={activity.activity_id}
            action={{
              id: activity.activity_id,
              name: activity.name,
              desc: activity.description,
              role: {
                id: 1,
                name: role,
                image: null,
              }
            }}
            onEdit={(newText) => onEditActivity(activity.activity_id, newText)}
            onDelete={() => onDeleteActivity(activity.activity_id)}
          />
        );
      })}
      {/* Add Activity Button - only visible on hover */}
      <Box sx={{ position: "absolute", bottom: 8, right: 8 }}>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={onAddActivity}
          sx={{
            backgroundColor: "#c8ff00",
            color: "#000000",
            fontWeight: "bold",
            opacity: isHovering ? 1 : 0,
            transition: "opacity 0.2s ease-in-out",
            "&:hover": {
              backgroundColor: "#b3e600",
            },
          }}
        >
          ADD
        </Button>
      </Box>
    </Card>
  );
};
