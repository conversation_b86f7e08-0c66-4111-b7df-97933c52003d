import ActivityCard from "@/components/mapCanvas/ActivityCard";
import { Action } from "@/types/map";
import type { Meta, StoryObj } from "@storybook/react";

// Mock data
const mockAction: Action = {
  id: "1",
  desc: "The customer inquires about opening a new savings account.",
  role: {
    id: 1,
    name: "Customer",
    image: "https://i.pravatar.cc/150?img=1",
    desc: "A customer interested in banking services",
    order: 1,
    map_role_id: 1,
    type: "customer",
  },
};

// Mock attachments
const mockAttachments: { name: string; image: string; status: string }[] = [
  {
    name: "Customer Record System",
    image:
      "https://wac-cdn.atlassian.com/dam/jcr:************************a470428cb6cd/Agile-report.png",
    status: "scheduled",
  },
  {
    name: "Customer Application Tracking",
    image:
      "https://wac-cdn.atlassian.com/dam/jcr:************************a470428cb6cd/Agile-report.png",
    status: "pending",
  },
  {
    name: "Additional Document",
    image:
      "https://wac-cdn.atlassian.com/dam/jcr:************************a470428cb6cd/Agile-report.png",
    status: "scheduled",
  },
];

const meta: Meta<typeof ActivityCard> = {
  title: "Composites/Map Canvas/Activity Card",
  component: ActivityCard,
  parameters: {
    layout: "centered",
    backgrounds: {
      default: "dark",
      values: [
        { name: "dark", value: "#1a1a1a" },
        { name: "light", value: "#ffffff" },
      ],
    },
  },
  tags: ["autodocs"],
  argTypes: {
    onClick: { action: "clicked" },
    onEdit: { action: "edited" },
    onDelete: { action: "deleted" },
    onEditPersona: { action: "persona edited" },
    attachments: {
      control: "object",
      description: "Array of attachment objects with name, image, and status properties",
    },
  },
  decorators: [
    (Story) => (
      <div style={{ width: "400px" }}>
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof ActivityCard>;

// Default state
export const Default: Story = {
  args: {
    action: mockAction,
  },
};

// With longer description text
export const LongDescription: Story = {
  args: {
    action: {
      ...mockAction,
      desc: "The customer inquires about opening a new savings account with competitive interest rates. They also want to know about minimum deposit requirements, monthly fees, and whether there are any promotional offers available for new accounts.",
    },
    onClick: () => console.log("Card clicked"),
    onEdit: (newText) => console.log("Edited:", newText),
    onDelete: () => console.log("Deleted"),
  },
};

// Without role image
export const NoRoleImage: Story = {
  args: {
    action: {
      ...mockAction,
      role: {
        id: 2,
        name: "Bank Representative",
        image: "",
        desc: "",
        order: 0,
        map_role_id: 0,
        type: "",
      },
    },
  },
};

export const WithAttachments: Story = {
  args: {
    action: {
      ...mockAction,
      desc: "I gather offline leads from my network, existing customers and the wider team",
    },
    attachments: mockAttachments,
    onClick: () => console.log("Card clicked"),
    onEdit: (newText) => console.log("Edited:", newText),
    onDelete: () => console.log("Deleted"),
  },
};
