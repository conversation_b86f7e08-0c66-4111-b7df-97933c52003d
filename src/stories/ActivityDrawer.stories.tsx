import ActivityDrawer from "@/components/mapCanvas/ActivityDrawer";
import { TouchpointItem } from "@/components/mapCanvas/TouchpointCard";
import { Box, Button } from "@mui/material";
import type { Meta, StoryObj } from "@storybook/react";
import React, { useState } from "react";

// Mock touchpoint data
const mockTouchpoints: TouchpointItem[] = [
  {
    id: "1",
    title: "ABC CRM",
    description: "Prospect document",
    status: "pending",
    isActive: true,
  },
  {
    id: "2",
    title: "ABC application",
    description: "Customer application tracker",
    status: "completed",
    isActive: true,
  },
  {
    id: "3",
    title: "Post",
    description: "Changeover pack",
    status: "pending",
    isActive: true,
  },
  {
    id: "4",
    title: "ATM",
    description: "Advertisement",
    status: "scheduled",
    isActive: true,
  },
  {
    id: "5",
    title: "Old System",
    description: "Legacy customer data",
    status: "completed",
    isActive: false,
  },
];

const meta: Meta<typeof ActivityDrawer> = {
  title: "Composites/Map Canvas/Activity Drawer",
  component: ActivityDrawer,
  parameters: {
    layout: "fullscreen",
    backgrounds: {
      default: "dark",
      values: [
        { name: "dark", value: "#1a1a1a" },
        { name: "light", value: "#ffffff" },
      ],
    },
  },
  tags: ["autodocs"],
  argTypes: {
    open: {
      control: "boolean",
      description: "Controls whether the drawer is open or closed",
    },
    onClose: { action: "closed" },
    onAddTouchpoint: { action: "add touchpoint" },
    onEditTouchpoint: { action: "edit touchpoint" },
    onDeleteTouchpoint: { action: "delete touchpoint" },
    userAvatar: {
      control: "text",
      description: "URL for the user avatar image",
    },
    userName: {
      control: "text",
      description: "Name of the user",
    },
    userDescription: {
      control: "text",
      description: "Description text for the user activity",
    },
    touchpoints: {
      control: "object",
      description: "Array of touchpoint items to display",
    },
  },
};

export default meta;
type Story = StoryObj<typeof ActivityDrawer>;

// Interactive wrapper component for stories
const ActivityDrawerWrapper: React.FC<{
  initialOpen?: boolean;
  touchpoints?: TouchpointItem[];
  userAvatar?: string;
  userName?: string;
  userDescription?: string;
}> = ({
  initialOpen = false,
  touchpoints = mockTouchpoints,
  userAvatar,
  userName,
  userDescription,
}) => {
  const [open, setOpen] = useState(initialOpen);

  return (
    <Box sx={{ height: "100vh", backgroundColor: "#1a1a1a", p: 2 }}>
      <Button variant="contained" onClick={() => setOpen(true)} sx={{ mb: 2 }}>
        Open Activity Drawer
      </Button>
      <ActivityDrawer
        open={open}
        onClose={() => setOpen(false)}
        touchpoints={touchpoints}
        userAvatar={userAvatar}
        userName={userName}
        userDescription={userDescription}
        onAddTouchpoint={() => console.log("Add touchpoint clicked")}
        onEditTouchpoint={(id) => console.log("Edit touchpoint:", id)}
        onDeleteTouchpoint={(id) => console.log("Delete touchpoint:", id)}
      />
    </Box>
  );
};

// Default closed state
export const Closed: Story = {
  render: () => <ActivityDrawerWrapper initialOpen={false} />,
  parameters: {
    docs: {
      description: {
        story: "The activity drawer in its closed state. Click the button to open it.",
      },
    },
  },
};

// Default open state
export const Open: Story = {
  render: () => <ActivityDrawerWrapper initialOpen={true} />,
  parameters: {
    docs: {
      description: {
        story:
          "The activity drawer in its open state showing customer activity and touchpoints. Click the expand button to toggle fullscreen mode.",
      },
    },
  },
};

// With custom user data
export const CustomUser: Story = {
  render: () => (
    <ActivityDrawerWrapper
      initialOpen={true}
      userAvatar="https://i.pravatar.cc/150?img=5"
      userName="Sarah Johnson"
      userDescription="I am reviewing the loan application documents and verifying the customer's financial information to ensure all requirements are met before proceeding with the approval process."
    />
  ),
  parameters: {
    docs: {
      description: {
        story: "Activity drawer with custom user avatar and description.",
      },
    },
  },
};

// With minimal touchpoints
export const MinimalTouchpoints: Story = {
  render: () => (
    <ActivityDrawerWrapper
      initialOpen={true}
      touchpoints={[
        {
          id: "1",
          title: "Customer Portal",
          description: "Online banking access",
          status: "completed",
          isActive: true,
        },
        {
          id: "2",
          title: "Support Ticket",
          description: "Account inquiry",
          status: "pending",
          isActive: true,
        },
      ]}
    />
  ),
  parameters: {
    docs: {
      description: {
        story: "Activity drawer with minimal touchpoint data.",
      },
    },
  },
};

// Empty touchpoints
export const EmptyTouchpoints: Story = {
  render: () => <ActivityDrawerWrapper initialOpen={true} touchpoints={[]} />,
  parameters: {
    docs: {
      description: {
        story: "Activity drawer with no touchpoints to show the empty state.",
      },
    },
  },
};

// With many touchpoints to test scrolling
export const ScrollableTouchpoints: Story = {
  render: () => (
    <ActivityDrawerWrapper
      initialOpen={true}
      touchpoints={[
        {
          id: "1",
          title: "Customer Portal Login",
          description: "Initial access to online banking platform",
          status: "completed",
          isActive: true,
          referenceImage: "https://via.placeholder.com/300x200",
          sections: [
            {
              id: "s1",
              title: "User Actions",
              items: [
                "Entered username",
                "Entered password",
                "Clicked login button",
                "Navigated to dashboard",
              ],
            },
            {
              id: "s2",
              title: "System Response",
              items: ["Validated credentials", "Loaded user profile", "Displayed account summary"],
            },
          ],
        },
        {
          id: "2",
          title: "Account Overview",
          description: "Reviewing account balance and recent transactions",
          status: "pending",
          isActive: true,
          sections: [
            {
              id: "s3",
              title: "Viewed Information",
              items: ["Current balance", "Recent transactions", "Pending payments"],
            },
          ],
        },
        {
          id: "3",
          title: "Transfer Funds",
          description: "Initiating money transfer between accounts",
          status: "scheduled",
          isActive: true,
          sections: [
            {
              id: "s4",
              title: "Transfer Details",
              items: [
                "Selected source account",
                "Selected destination account",
                "Entered amount",
                "Set transfer date",
              ],
            },
          ],
        },
        {
          id: "4",
          title: "Support Chat",
          description: "Customer service interaction for account inquiry",
          status: "completed",
          isActive: true,
          sections: [
            {
              id: "s5",
              title: "Chat History",
              items: ["Initiated chat", "Explained issue", "Received assistance", "Issue resolved"],
            },
          ],
        },
        {
          id: "5",
          title: "Document Upload",
          description: "Uploading required verification documents",
          status: "pending",
          isActive: true,
          referenceImage: "https://via.placeholder.com/300x150",
          sections: [
            {
              id: "s6",
              title: "Documents",
              items: ["Driver's license", "Proof of income", "Bank statement"],
            },
          ],
        },
        {
          id: "6",
          title: "Mobile App Access",
          description: "Using mobile banking application",
          status: "completed",
          isActive: false,
          sections: [
            {
              id: "s7",
              title: "Mobile Features",
              items: ["Check balance", "Mobile deposit", "ATM locator", "Push notifications"],
            },
          ],
        },
      ]}
    />
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Activity drawer with many touchpoints that have expandable content to demonstrate scrolling functionality when cards are expanded.",
      },
    },
  },
};

// With archived touchpoints
export const WithArchivedTouchpoints: Story = {
  render: () => (
    <ActivityDrawerWrapper
      initialOpen={true}
      touchpoints={[
        ...mockTouchpoints,
        {
          id: "6",
          title: "Legacy System",
          description: "Old customer management system",
          status: "completed",
          isActive: false,
        },
        {
          id: "7",
          title: "Paper Forms",
          description: "Manual application process",
          status: "completed",
          isActive: false,
        },
      ]}
    />
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Activity drawer with both active and archived touchpoints. Switch to the 'ARCHIVED' tab to see archived items.",
      },
    },
  },
};
