import MomentCard from "@/components/mapCanvas/MomentCard";
import { Component } from "@/types/map";
import type { Meta, StoryObj } from "@storybook/react";

// Mock components
const mockComponents: Component[] = [
  {
    id: 101,
    numbering: "CC1.3",
    name: "Service Discovery",
    description: "The process by which a customer learns about available products or services.",
    is_active: true,
    level: 2,
    framework: 1, // Customer view framework
  },
  {
    id: 102,
    numbering: "BC2.1",
    name: "Customer Acquisition",
    description: "The business process of acquiring new customers.",
    is_active: true,
    level: 2,
    framework: 2, // Business view framework
  },
];

const meta: Meta<typeof MomentCard> = {
  title: "Composites/Map Canvas/Moment Card",
  component: MomentCard,
  parameters: {
    layout: "centered",
    backgrounds: {
      default: "dark",
      values: [
        { name: "dark", value: "#1a1a1a" },
        { name: "light", value: "#ffffff" },
      ],
    },
  },
  tags: ["autodocs"],
  argTypes: {
    onClick: { action: "clicked" },
    onEdit: { action: "edited" },
    onImageUpdate: { action: "image updated" },
  },
  decorators: [
    (Story) => (
      <div style={{ width: "770px", height: "600px" }}>
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof MomentCard>;

// Default state
export const Default: Story = {
  args: {
    id: 1,
    name: "Product Discovery",
    title: "Noticing a home loan offer",
    desc: "Customer discovers and explores available products",
    image: "https://images.unsplash.com/photo-**********-0cfed4f6a45d?w=400&h=300&fit=crop",
    components: mockComponents,
    onEdit: (updates) => console.log("Edit moment:", updates),
  },
};

// Without image
export const NoImage: Story = {
  args: {
    id: 2,
    name: "Product Discovery",
    title: "Exploring banking options",
    desc: "Customer discovers and explores available products",
    components: mockComponents,
    onEdit: (updates) => console.log("Edit moment:", updates),
  },
};

// Long description
export const LongDescription: Story = {
  args: {
    id: 3,
    name: "Product Discovery",
    desc: "Customer discovers and explores available products through various channels including online marketplace, social media recommendations, search engines, and word-of-mouth referrals. This phase involves initial awareness and interest generation.",
    components: mockComponents,
    onEdit: (updates) => console.log("Edit moment:", updates),
  },
};

// No description
export const NoDescription: Story = {
  args: {
    id: 4,
    name: "Product Discovery",
    desc: "",
    components: mockComponents,
    onEdit: (updates) => console.log("Edit moment:", updates),
  },
};

// With interactions
export const WithInteractions: Story = {
  args: {
    id: 5,
    name: "Product Discovery",
    desc: "Customer discovers and explores available products",
    image: "https://images.unsplash.com/photo-**********-0cfed4f6a45d?w=400&h=300&fit=crop",
    components: mockComponents,
    onEdit: (updates) => console.log("Edit moment:", updates),
    onImageUpdate: (momentId, imageUrl) => console.log("Update image:", momentId, imageUrl),
  },
};

// Different sequence numbers
export const HighSequence: Story = {
  args: {
    id: 6,
    name: "Purchase Completion",
    desc: "Customer completes the purchase process",
    image: "https://images.unsplash.com/photo-1556740738-b6a63e27c4df?w=400&h=300&fit=crop",
    components: mockComponents,
    onEdit: (updates) => console.log("Edit moment:", updates),
  },
};

// Many components
export const ManyComponents: Story = {
  args: {
    id: 7,
    name: "Product Discovery",
    desc: "Customer discovers and explores available products",
    components: [...mockComponents, ...mockComponents, ...mockComponents],
    onEdit: (updates) => console.log("Edit moment:", updates),
  },
};

// Single component
export const SingleComponent: Story = {
  args: {
    id: 8,
    name: "Product Discovery",
    desc: "Customer discovers and explores available products",
    components: [mockComponents[0]],
    onEdit: (updates) => console.log("Edit moment:", updates),
  },
};

// No components
export const NoComponents: Story = {
  args: {
    id: 9,
    name: "Product Discovery",
    desc: "Customer discovers and explores available products",
    components: [],
    onEdit: (updates) => console.log("Edit moment:", updates),
  },
};
