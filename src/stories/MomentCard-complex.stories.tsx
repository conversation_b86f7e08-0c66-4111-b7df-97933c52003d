import MomentCard from "@/components/mapCanvas/MomentCard";
import { MapCanvasProvider } from "@/context/MapCanvasProvider";
import { Component } from "@/types/map";
import type { Meta, StoryObj } from "@storybook/react";

// Mock components
const mockComponents: Component[] = [
  {
    id: 101,
    numbering: "CC1.3",
    name: "Service Discovery",
    description: "The process by which a customer learns about available products or services.",
    is_active: true,
    level: 2,
    framework: 1, // Customer view framework
  },
  {
    id: 102,
    numbering: "BC2.1",
    name: "Customer Acquisition",
    description: "The business process of acquiring new customers.",
    is_active: true,
    level: 2,
    framework: 2, // Business view framework
  },
];

// Wrap with provider
const MomentCardWithProvider = (args: any) => (
  <MapCanvasProvider>
    <MomentCard {...args} />
  </MapCanvasProvider>
);

// Define meta
const meta: Meta<typeof MomentCard> = {
  title: "Composites/Map Canvas/Moment Card Complex",
  component: MomentCard,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  decorators: [
    (Story) => (
      <div style={{ width: "60rem", height: "600px" }}>
        <Story />
      </div>
    ),
  ],
  render: MomentCardWithProvider, // Use the wrapped component for all stories
};

export default meta;
type Story = StoryObj<typeof MomentCard>;

// Default story with no image
export const Default: Story = {
  args: {
    id: 1,
    name: "Discovery Moment",
    title: "Noticing a home loan offer",
    desc: "In this moment, customers explore available banking options and gather information about different account types and services that might meet their financial needs.",
    components: mockComponents,
    onEdit: (moment) => console.log("Moment edited:", moment),
  },
};

// Story with image
export const WithImage: Story = {
  args: {
    id: 2,
    name: "Evaluation Moment",
    desc: "Customers compare different banking options based on fees, interest rates, and convenience factors to determine which best suits their needs.",
    image:
      "https://images.unsplash.com/photo-**********-d5d88e9218df?q=80&w=2070&auto=format&fit=crop",
    components: mockComponents,
    onEdit: (moment) => console.log("Moment edited:", moment),
    onImageUpdate: (id, imageUrl) => console.log(`Image updated for moment ${id}:`, imageUrl),
  },
};

// Story with no components
export const WithNoComponents: Story = {
  args: {
    id: 4,
    name: "Onboarding Moment",
    desc: "The process by which new customers are introduced to the bank's services and systems after they have made the decision to open an account.",
    components: [],
    onEdit: (moment) => console.log("Moment edited:", moment),
  },
};
