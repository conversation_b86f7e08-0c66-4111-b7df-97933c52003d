import { ActivityCell } from "@/components/mapCanvas/ActivityCell";
import { Activity } from "@/types/grid.types";
import type { Meta, StoryObj } from "@storybook/react";

// Mock activities data
const mockActivities: Activity[] = [
  {
    id: "1",
    name: "Browse Products",
    desc: "Customer looks through available products",
    role: {
      id: 1,
      name: "Customer",
      image: "https://i.pravatar.cc/150?img=1",
      desc: "Primary customer persona",
      order: 1,
      map_role_id: 1,
      type: "customer",
    },
  },
  {
    id: "2", 
    name: "Add to Cart",
    desc: "Customer adds selected items to shopping cart",
    role: {
      id: 1,
      name: "Customer", 
      image: "https://i.pravatar.cc/150?img=1",
      desc: "Primary customer persona",
      order: 1,
      map_role_id: 1,
      type: "customer",
    },
  },
];

const meta: Meta<typeof ActivityCell> = {
  title: "Composites/Map Canvas/Activity Cell",
  component: ActivityCell,
  parameters: {
    layout: "centered",
    backgrounds: {
      default: "dark",
      values: [
        { name: "dark", value: "#1a1a1a" },
        { name: "light", value: "#ffffff" },
      ],
    },
  },
  tags: ["autodocs"],
  argTypes: {
    phaseId: { control: "text" },
    height: { control: "number" },
    type: { 
      control: "select",
      options: ["action", "activity"]
    },
    role: { control: "text" },
    onEditActivity: { action: "activity edited" },
    onDeleteActivity: { action: "activity deleted" },
    onAddActivity: { action: "activity added" },
  },
  decorators: [
    (Story) => (
      <div style={{ width: "400px", height: "300px" }}>
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof ActivityCell>;

// Default state with activities
export const Default: Story = {
  args: {
    phaseId: "phase-1",
    height: 250,
    type: "activity",
    role: "Customer",
    activities: mockActivities,
  },
};

// Empty state
export const Empty: Story = {
  args: {
    phaseId: "phase-1", 
    height: 250,
    type: "activity",
    role: "Customer",
    activities: [],
  },
};

// Front Stage role
export const FrontStage: Story = {
  args: {
    phaseId: "phase-1",
    height: 250, 
    type: "activity",
    role: "Front Stage",
    activities: [
      {
        id: "3",
        name: "Process Order",
        desc: "Staff processes the customer order",
        role: {
          id: 2,
          name: "Front Stage Staff",
          image: "https://i.pravatar.cc/150?img=2",
          desc: "Customer-facing staff",
          order: 2,
          map_role_id: 2,
          type: "front_stage",
        },
      },
    ],
  },
};

// Back Stage role
export const BackStage: Story = {
  args: {
    phaseId: "phase-1",
    height: 250,
    type: "activity", 
    role: "Back Stage",
    activities: [
      {
        id: "4",
        name: "Inventory Check",
        desc: "System checks product availability",
        role: {
          id: 3,
          name: "Back Stage System",
          image: "",
          desc: "Backend operations",
          order: 3,
          map_role_id: 3,
          type: "back_stage",
        },
      },
    ],
  },
};

// Tall cell with many activities
export const ManyActivities: Story = {
  args: {
    phaseId: "phase-1",
    height: 400,
    type: "activity",
    role: "Customer",
    activities: [
      ...mockActivities,
      {
        id: "5",
        name: "Read Reviews",
        desc: "Customer reads product reviews from other customers",
        role: {
          id: 1,
          name: "Customer",
          image: "https://i.pravatar.cc/150?img=1", 
          desc: "Primary customer persona",
          order: 1,
          map_role_id: 1,
          type: "customer",
        },
      },
      {
        id: "6", 
        name: "Compare Prices",
        desc: "Customer compares prices across different options",
        role: {
          id: 1,
          name: "Customer",
          image: "https://i.pravatar.cc/150?img=1",
          desc: "Primary customer persona", 
          order: 1,
          map_role_id: 1,
          type: "customer",
        },
      },
    ],
  },
};