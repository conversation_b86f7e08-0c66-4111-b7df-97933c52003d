import React, { createContext, ReactNode, useContext, useReducer } from "react";
import type { 
  MapDetails, 
  PhaseDetails,
  MomentDetails, 
  ActivityDetails
} from "@/types/api/client";

interface MapCanvasState extends Omit<MapDetails, 'phases' | 'moments'> {
  phases: PhaseDetails[];
  moments: MomentDetails[];
  loading: boolean;
  error: string | null;
}

export enum MapCanvasActionType {
  // Map actions
  SET_MAP_DATA = "SET_MAP_DATA",
  SET_LOADING = "SET_LOADING",
  SET_ERROR = "SET_ERROR",
  
  // Phase actions
  CREATE_PHASE = "CREATE_PHASE",
  UPDATE_PHASE = "UPDATE_PHASE",
  DELETE_PHASE = "DELETE_PHASE",
  
  // Moment actions
  CREATE_MOMENT = "CREATE_MOMENT",
  UPDATE_MOMENT = "UPDATE_MOMENT",
  DELETE_MOMENT = "DELETE_MOMENT",
  
  // Activity actions
  CREATE_ACTIVITY = "CREATE_ACTIVITY",
  UPDATE_ACTIVITY = "UPDATE_ACTIVITY",
  DELETE_ACTIVITY = "DELETE_ACTIVITY",
}

type MapCanvasAction =
  | { type: MapCanvasActionType.SET_MAP_DATA; payload: MapDetails }
  | { type: MapCanvasActionType.SET_LOADING; payload: boolean }
  | { type: MapCanvasActionType.SET_ERROR; payload: string | null }
  
  // Phase actions
  | { type: MapCanvasActionType.CREATE_PHASE; payload: PhaseDetails }
  | { type: MapCanvasActionType.UPDATE_PHASE; payload: { phaseId: string; updates: Partial<PhaseDetails> } }
  | { type: MapCanvasActionType.DELETE_PHASE; payload: string }
  
  // Moment actions
  | { type: MapCanvasActionType.CREATE_MOMENT; payload: MomentDetails }
  | { type: MapCanvasActionType.UPDATE_MOMENT; payload: { momentId: string; updates: Partial<MomentDetails> } }
  | { type: MapCanvasActionType.DELETE_MOMENT; payload: string }
  
  // Activity actions
  | { type: MapCanvasActionType.CREATE_ACTIVITY; payload: { momentId: string; activity: ActivityDetails } }
  | { type: MapCanvasActionType.UPDATE_ACTIVITY; payload: { activityId: string; updates: Partial<ActivityDetails> } }
  | { type: MapCanvasActionType.DELETE_ACTIVITY; payload: string };

const initialState: MapCanvasState = {
  map_id: "",
  organization_id: "",
  customer_goal: {
    customer_goal_id: "",
    organization_id: "",
    name: "",
    description: "",
    category: "",
    priority: "medium",
    status: "active",
    created_by: {
      user_id: "",
      name: "",
      profile_image_url: "",
      timestamp: ""
    },
    updated_by: {
      user_id: "",
      name: "",
      profile_image_url: "",
      timestamp: ""
    }
  },
  name: "",
  description: "",
  division: "",
  state: "draft",
  status: "active",
  created_by: {
    user_id: "",
    name: "",
    profile_image_url: "",
    timestamp: ""
  },
  updated_by: {
    user_id: "",
    name: "",
    profile_image_url: "",
    timestamp: ""
  },
  phases: [],
  moments: [],
  personas: [],
  components: [],
  statistics: {
    total_phases: 0,
    total_moments: 0,
    total_actions: 0,
    total_personas: 0,
    total_components: 0
  },
  loading: false,
  error: null
};

function mapCanvasReducer(state: MapCanvasState, action: MapCanvasAction): MapCanvasState {
  switch (action.type) {
    case MapCanvasActionType.SET_MAP_DATA:
      return {
        ...state,
        ...action.payload,
        phases: action.payload.phases.map(phase => ({
          ...phase,
          moment_ids: phase.moment_ids || []
        })),
        loading: false,
        error: null
      };
      
    case MapCanvasActionType.SET_LOADING:
      return {
        ...state,
        loading: action.payload
      };
      
    case MapCanvasActionType.SET_ERROR:
      return {
        ...state,
        error: action.payload,
        loading: false
      };

    // Phase actions
    case MapCanvasActionType.CREATE_PHASE:
      return {
        ...state,
        phases: [...state.phases, action.payload],
        statistics: {
          ...(state.statistics as any),
          total_phases: (state.statistics as any).total_phases + 1
        }
      };
      
    case MapCanvasActionType.UPDATE_PHASE:
      return {
        ...state,
        phases: state.phases.map(phase =>
          phase.phase_id === action.payload.phaseId
            ? { ...phase, ...action.payload.updates }
            : phase
        )
      };
      
    case MapCanvasActionType.DELETE_PHASE:
      const phaseMomentIds = state.phases.find(p => p.phase_id === action.payload)?.moment_ids || [];
      return {
        ...state,
        phases: state.phases.filter(phase => phase.phase_id !== action.payload),
        moments: state.moments.filter(moment => !phaseMomentIds.includes(moment.moment_id)),
        statistics: {
          ...(state.statistics as any),
          total_phases: Math.max(0, (state.statistics as any).total_phases - 1),
          total_moments: Math.max(0, (state.statistics as any).total_moments - phaseMomentIds.length)
        }
      };

    // Moment actions
    case MapCanvasActionType.CREATE_MOMENT:
      return {
        ...state,
        moments: [...state.moments, action.payload],
        statistics: {
          ...(state.statistics as any),
          total_moments: (state.statistics as any).total_moments + 1
        }
      };
      
    case MapCanvasActionType.UPDATE_MOMENT:
      return {
        ...state,
        moments: state.moments.map(moment =>
          moment.moment_id === action.payload.momentId
            ? { ...moment, ...action.payload.updates }
            : moment
        )
      };
      
    case MapCanvasActionType.DELETE_MOMENT:
      const momentToDelete = state.moments.find(m => m.moment_id === action.payload);
      const totalActivitiesInMoment = momentToDelete ? 
        Object.values(momentToDelete.activities).flat().length : 0;
        
      return {
        ...state,
        moments: state.moments.filter(moment => moment.moment_id !== action.payload),
        phases: state.phases.map(phase => ({
          ...phase,
          moment_ids: phase.moment_ids.filter(id => id !== action.payload)
        })),
        statistics: {
          ...(state.statistics as any),
          total_moments: Math.max(0, (state.statistics as any).total_moments - 1),
          total_actions: Math.max(0, (state.statistics as any).total_actions - totalActivitiesInMoment)
        }
      };

    // Activity actions
    case MapCanvasActionType.CREATE_ACTIVITY:
      return {
        ...state,
        moments: state.moments.map(moment =>
          moment.moment_id === action.payload.momentId
            ? {
                ...moment,
                activities: {
                  ...moment.activities,
                  [action.payload.activity.activity_type]: [
                    ...moment.activities[action.payload.activity.activity_type],
                    action.payload.activity
                  ]
                }
              }
            : moment
        ),
        statistics: {
          ...(state.statistics as any),
          total_actions: (state.statistics as any).total_actions + 1
        }
      };
      
    case MapCanvasActionType.UPDATE_ACTIVITY:
      return {
        ...state,
        moments: state.moments.map(moment => ({
          ...moment,
          activities: {
            customer: moment.activities.customer.map(activity =>
              activity.activity_id === action.payload.activityId
                ? { ...activity, ...action.payload.updates }
                : activity
            ),
            front_stage: moment.activities.front_stage.map(activity =>
              activity.activity_id === action.payload.activityId
                ? { ...activity, ...action.payload.updates }
                : activity
            ),
            back_stage: moment.activities.back_stage.map(activity =>
              activity.activity_id === action.payload.activityId
                ? { ...activity, ...action.payload.updates }
                : activity
            ),
            system: moment.activities.system.map(activity =>
              activity.activity_id === action.payload.activityId
                ? { ...activity, ...action.payload.updates }
                : activity
            )
          }
        }))
      };
      
    case MapCanvasActionType.DELETE_ACTIVITY:
      return {
        ...state,
        moments: state.moments.map(moment => ({
          ...moment,
          activities: {
            customer: moment.activities.customer.filter(activity => 
              activity.activity_id !== action.payload
            ),
            front_stage: moment.activities.front_stage.filter(activity => 
              activity.activity_id !== action.payload
            ),
            back_stage: moment.activities.back_stage.filter(activity => 
              activity.activity_id !== action.payload
            ),
            system: moment.activities.system.filter(activity => 
              activity.activity_id !== action.payload
            )
          }
        })),
        statistics: {
          ...(state.statistics as any),
          total_actions: Math.max(0, (state.statistics as any).total_actions - 1)
        }
      };

    default:
      return state;
  }
}

const MapCanvasContext = createContext<{
  state: MapCanvasState;
  dispatch: React.Dispatch<MapCanvasAction>;
}>({
  state: initialState,
  dispatch: () => undefined,
});

export const useMapCanvas = () => useContext(MapCanvasContext);

export const MapCanvasProvider = ({ children }: { children: ReactNode }) => {
  const [state, dispatch] = useReducer(mapCanvasReducer, initialState);

  return (
    <MapCanvasContext.Provider value={{ state, dispatch }}>
      {children}
    </MapCanvasContext.Provider>
  );
};

// Action creators that sync with API hooks
export const mapCanvasActions = {
  // Phase actions
  createPhase: (phase: PhaseDetails) => ({
    type: MapCanvasActionType.CREATE_PHASE,
    payload: phase
  } as const),
  
  updatePhase: (phaseId: string, updates: Partial<PhaseDetails>) => ({
    type: MapCanvasActionType.UPDATE_PHASE,
    payload: { phaseId, updates }
  } as const),
  
  deletePhase: (phaseId: string) => ({
    type: MapCanvasActionType.DELETE_PHASE,
    payload: phaseId
  } as const),
  
  // Moment actions
  createMoment: (moment: MomentDetails) => ({
    type: MapCanvasActionType.CREATE_MOMENT,
    payload: moment
  } as const),
  
  updateMoment: (momentId: string, updates: Partial<MomentDetails>) => ({
    type: MapCanvasActionType.UPDATE_MOMENT,
    payload: { momentId, updates }
  } as const),
  
  deleteMoment: (momentId: string) => ({
    type: MapCanvasActionType.DELETE_MOMENT,
    payload: momentId
  } as const),
  
  // Activity actions
  createActivity: (momentId: string, activity: ActivityDetails) => ({
    type: MapCanvasActionType.CREATE_ACTIVITY,
    payload: { momentId, activity }
  } as const),
  
  updateActivity: (activityId: string, updates: Partial<ActivityDetails>) => ({
    type: MapCanvasActionType.UPDATE_ACTIVITY,
    payload: { activityId, updates }
  } as const),
  
  deleteActivity: (activityId: string) => ({
    type: MapCanvasActionType.DELETE_ACTIVITY,
    payload: activityId
  } as const),
  
  // Map data actions
  setMapData: (mapData: MapDetails) => ({
    type: MapCanvasActionType.SET_MAP_DATA,
    payload: mapData
  } as const),
  
  setLoading: (loading: boolean) => ({
    type: MapCanvasActionType.SET_LOADING,
    payload: loading
  } as const),
  
  setError: (error: string | null) => ({
    type: MapCanvasActionType.SET_ERROR,
    payload: error
  } as const)
};