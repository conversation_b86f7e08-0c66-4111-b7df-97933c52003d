import AuthService from "@/services/AuthService";
import BaseService from "@/services/BaseService";
import { useRouter } from "next/navigation";
import React, { createContext, useCallback, useContext, useEffect, useState } from "react";

const SESSION_TIMER_THRESHOLD = 8 * 60 * 60 * 1000; // 8 hours in milliseconds
const CHECK_INTERVAL = 1000 * 60 * 10; // 10 minutes interval

interface UserInfo {
  id: string;
  name: string;
  color: string;
}

interface AuthContextType {
  isAuthenticated: boolean;
  userInfo: UserInfo | null;
  login: (username: string, password: string) => Promise<void>;
  logout: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const router = useRouter();

  const logout = useCallback(() => {
    AuthService.logout();
    BaseService.clearAuthToken();
    localStorage.removeItem("lastLoginTime");
    localStorage.removeItem("authToken");
    localStorage.removeItem("userInfo");
    setUserInfo(null);
    setIsAuthenticated(false);
    router.push("/login");
  }, [router]);

  const checkSessionValidity = useCallback(() => {
    const lastLoginTime = localStorage.getItem("lastLoginTime");
    const currentTime = Date.now();

    if (lastLoginTime) {
      const timeSinceLastLogin = currentTime - Number(lastLoginTime);
      if (timeSinceLastLogin > SESSION_TIMER_THRESHOLD) {
        logout();
      }
    } else {
      logout(); // If no last login time is found, force logout
    }
  }, [logout]);

  const setLogoutTimer = useCallback(() => {
    // Periodically check session validity
    const interval = setInterval(checkSessionValidity, CHECK_INTERVAL);
    return () => clearInterval(interval); // Cleanup on unmount
  }, [checkSessionValidity]);

  useEffect(() => {
    const initializeAuth = () => {
      const authToken = localStorage.getItem("authToken");
      const lastLoginTime = localStorage.getItem("lastLoginTime");
      const storedUserInfo = localStorage.getItem("userInfo");

      if (authToken && lastLoginTime) {
        setIsAuthenticated(true);
        if (storedUserInfo) {
          setUserInfo(JSON.parse(storedUserInfo));
        }
        BaseService.setAuthToken(authToken);
        checkSessionValidity();
        return setLogoutTimer();
      } else {
        router.push("/login");
      }
    };

    const cleanup = initializeAuth();
    return () => {
      cleanup?.();
    };
  }, [router, setLogoutTimer, checkSessionValidity]);

  const login = async (username: string, password: string) => {
    const response = await AuthService.login(username, password);
    const newUserInfo: UserInfo = {
      id: crypto.randomUUID(),
      name: username,
      color: "#" + Math.floor(Math.random() * 16777215).toString(16),
    };

    localStorage.setItem("lastLoginTime", Date.now().toString());
    localStorage.setItem("userInfo", JSON.stringify(newUserInfo));
    localStorage.setItem("authToken", response.access);
    BaseService.setAuthToken(response.access);
    setUserInfo(newUserInfo);
    setIsAuthenticated(true);

    // Dispatch a custom event to notify other components about the login
    const loginEvent = new CustomEvent("user-login", {
      detail: { user: newUserInfo },
    });
    window.dispatchEvent(loginEvent);

    checkSessionValidity();
    setLogoutTimer();
    router.push("/");
  };

  return (
    <AuthContext.Provider value={{ isAuthenticated, userInfo, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
};

// Hook for consuming the AuthContext
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
