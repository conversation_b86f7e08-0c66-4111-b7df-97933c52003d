{"permissions": {"allow": ["Bash(npm install:*)", "Bash(npm run build:*)", "WebFetch(domain:blog.strateos.com)", "WebFetch(domain:github.com)", "WebFetch(domain:www.npmjs.com)", "WebFetch(domain:bettertyped.github.io)", "Bash(rm:*)", "Bash(npm run dev:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "mcp__ide__getDiagnostics", "Bash(npm run:*)", "Bash(find:*)", "Bash(npx tsc:*)", "<PERSON><PERSON>(mv:*)", "Bash(grep:*)", "Bash(node:*)"], "deny": []}}