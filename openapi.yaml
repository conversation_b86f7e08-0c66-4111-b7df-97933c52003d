openapi: 3.0.3
info:
  title: Mulapin API v2.0
  version: "2"
  description: |
    Enterprise customer journey mapping platform API.

    **Organization Context:**
    - All endpoints automatically use organization from JWT token
    - Super admin users can override using X-Organization-Id header
    - Regular users are restricted to their organization only

    **Authentication:**
    - JWT token contains user_id, organization_id, role, and permissions
    - <PERSON>ken must be included in Authorization header as Bear<PERSON> token
  contact:
    name: Mulapin API Support
    email: <EMAIL>

servers:
  - url: https://api.mulapin.com/v2
    description: Production API v2

security:
  - BearerAuth: []

paths:
  /auth/login:
    post:
      tags: [Authentication]
      summary: User Login
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [email, password]
              properties:
                email:
                  type: string
                  format: email
                  example: "<EMAIL>"
                password:
                  type: string
                  example: "SecurePass123!"
      responses:
        "200":
          description: Login successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  access_token:
                    type: string
                    example: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoidXNyX3NhcmFoX2pvaG5zb25fMDAxIiwib3JnYW5pemF0aW9uX2lkIjoiYWNtZS1maW5hbmNpYWwtMjAyNCIsInJvbGUiOiJ3cml0ZXIiLCJleHAiOjE3MDUzNjAwMDB9.Kd3Xz9H_QfK5LmN8Qr7sT1gY2hZ3jK4mP6nO9wE1rV5"
                  expires_in:
                    type: integer
                    example: 3600
                  user:
                    $ref: "#/components/schemas/UserProfile"
        "401":
          $ref: "#/components/responses/Unauthorized"

  /admin/organizations:
    get:
      tags: [Super Admin]
      summary: Get All Organizations
      description: Super admin only - retrieve all organizations
      responses:
        "200":
          description: Organizations retrieved
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Organization"

    post:
      tags: [Super Admin]
      summary: Create Organization
      description: Super admin only - create new organization
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/OrganizationCreate"
      responses:
        "201":
          description: Organization created
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Organization"

  /admin/organizations/{orgId}:
    get:
      tags: [Super Admin]
      summary: Get Organization Details
      parameters:
        - name: orgId
          in: path
          required: true
          schema:
            type: string
            example: "acme-financial-2024"
      responses:
        "200":
          description: Organization details
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OrganizationDetails"

    put:
      tags: [Super Admin]
      summary: Update Organization
      parameters:
        - name: orgId
          in: path
          required: true
          schema:
            type: string
            example: "acme-financial-2024"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/OrganizationUpdate"
      responses:
        "200":
          description: Organization updated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Organization"

  /organization:
    get:
      tags: [Organization]
      summary: Get Current Organization
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
      responses:
        "200":
          description: Organization details
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OrganizationDetails"

    put:
      tags: [Organization]
      summary: Update Current Organization
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/OrganizationUpdate"
      responses:
        "200":
          description: Organization updated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Organization"

  /users:
    get:
      tags: [Users]
      summary: Get Organization Users
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
      responses:
        "200":
          description: Users retrieved
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/User"

    post:
      tags: [Users]
      summary: Create User
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UserCreate"
      responses:
        "201":
          description: User created
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/User"

  /users/{userId}:
    get:
      tags: [Users]
      summary: Get User Details
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
        - name: userId
          in: path
          required: true
          schema:
            type: string
      responses:
        "200":
          description: User details
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UserDetails"

    put:
      tags: [Users]
      summary: Update User
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
        - name: userId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UserUpdate"
      responses:
        "200":
          description: User updated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/User"

    delete:
      tags: [Users]
      summary: Delete User
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
        - name: userId
          in: path
          required: true
          schema:
            type: string
      responses:
        "204":
          description: User deleted

  /customer-goals:
    get:
      tags: [Customer Goals]
      summary: Get Customer Goals
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
      responses:
        "200":
          description: Customer goals retrieved
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/CustomerGoal"

    post:
      tags: [Customer Goals]
      summary: Create Customer Goal
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CustomerGoalCreate"
      responses:
        "201":
          description: Customer goal created
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CustomerGoal"

  /customer-goals/{customer_goal_id}:
    get:
      tags: [Customer Goals]
      summary: Get Customer Goal
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
        - name: customer_goal_id
          in: path
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Customer goal details
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CustomerGoalDetails"

    put:
      tags: [Customer Goals]
      summary: Update Customer Goal
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
        - name: customer_goal_id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CustomerGoalUpdate"
      responses:
        "200":
          description: Customer goal updated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CustomerGoal"

    delete:
      tags: [Customer Goals]
      summary: Delete Customer Goal
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
        - name: customer_goal_id
          in: path
          required: true
          schema:
            type: string
      responses:
        "204":
          description: Customer goal deleted

  /maps:
    get:
      tags: [Maps]
      summary: Get Maps
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
      responses:
        "200":
          description: Map retrieved
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Map"

    post:
      tags: [Maps]
      summary: Create Map
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/MapCreate"
      responses:
        "201":
          description: Map created
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Map"

  /maps/{mapId}:
    get:
      tags: [Maps]
      summary: Get Map
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
        - name: mapId
          in: path
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Map details
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MapDetails"

    put:
      tags: [Maps]
      summary: Update Map
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
        - name: mapId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/MapUpdate"
      responses:
        "200":
          description: Map updated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Map"

    delete:
      tags: [Maps]
      summary: Delete Map
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
        - name: mapId
          in: path
          required: true
          schema:
            type: string
      responses:
        "204":
          description: Map deleted

  /maps/{mapId}/phases:
    get:
      tags: [Phases]
      summary: Get Map Phases
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
        - name: mapId
          in: path
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Phases retrieved
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Phase"

    post:
      tags: [Phases]
      summary: Create Phase
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
        - name: mapId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PhaseCreate"
      responses:
        "201":
          description: Phase created
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Phase"

  /phases/{phaseId}:
    get:
      tags: [Phases]
      summary: Get Phase
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
        - name: phaseId
          in: path
          required: true
          schema:
            type: string
            example: "phase_research_products"
      responses:
        "200":
          description: Phase details
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PhaseDetails"

    put:
      tags: [Phases]
      summary: Update Phase
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
        - name: phaseId
          in: path
          required: true
          schema:
            type: string
            example: "phase_research_products"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PhaseUpdate"
      responses:
        "200":
          description: Phase updated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Phase"

    delete:
      tags: [Phases]
      summary: Delete Phase
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
        - name: phaseId
          in: path
          required: true
          schema:
            type: string
            example: "phase_research_products"
      responses:
        "204":
          description: Phase deleted

  /moments:
    get:
      tags: [Moments]
      summary: Get Organization Moments
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
      responses:
        "200":
          description: Moments retrieved
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Moment"

  /moments/batch:
    post:
      tags: [Moments]
      summary: Get Multiple Moments by IDs
      description: Retrieve multiple moments by providing an array of moment IDs in the request body
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [moment_ids]
              properties:
                moment_ids:
                  type: array
                  items:
                    type: string
                  description: Array of moment IDs to retrieve
                  example:
                    [
                      "moment_compare_prices_online",
                      "moment_check_product_reviews",
                      "moment_add_items_to_cart",
                    ]
      responses:
        "200":
          description: Moments retrieved
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/MomentDetails"

  /maps/{mapId}/phases/{phaseId}/moments:
    get:
      tags: [Moments]
      summary: Get Phase Moments
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
        - name: mapId
          in: path
          required: true
          schema:
            type: string
        - name: phaseId
          in: path
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Moments retrieved
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Moment"

    post:
      tags: [Moments]
      summary: Create Moment
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
        - name: mapId
          in: path
          required: true
          schema:
            type: string
        - name: phaseId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/MomentCreate"
      responses:
        "201":
          description: Moment created
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Moment"

  /moments/{momentId}:
    get:
      tags: [Moments]
      summary: Get Moment
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
        - name: momentId
          in: path
          required: true
          schema:
            type: string
            example: "moment_compare_prices_online"
      responses:
        "200":
          description: Moment details
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MomentDetails"

    put:
      tags: [Moments]
      summary: Update Moment
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
        - name: momentId
          in: path
          required: true
          schema:
            type: string
            example: "moment_compare_prices_online"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/MomentUpdate"
      responses:
        "200":
          description: Moment updated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Moment"

    delete:
      tags: [Moments]
      summary: Delete Moment
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
        - name: momentId
          in: path
          required: true
          schema:
            type: string
            example: "moment_compare_prices_online"
      responses:
        "204":
          description: Moment deleted

  /moments/{momentId}/activities:
    get:
      tags: [Activities]
      summary: Get Moment Activities
      description: Retrieve all activities for a moment, grouped by activity type
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
        - name: momentId
          in: path
          required: true
          schema:
            type: string
            example: "moment_compare_prices_online"
      responses:
        "200":
          description: Activities retrieved grouped by activity type
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ActivitiesByType"

    post:
      tags: [Activities]
      summary: Create Activity in Moment
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
        - name: momentId
          in: path
          required: true
          schema:
            type: string
            example: "moment_compare_prices_online"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ActivityCreate"
      responses:
        "201":
          description: Activity created
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Activity"

  /activities/{activityId}:
    get:
      tags: [Activities]
      summary: Get Activity
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
        - name: activityId
          in: path
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Activity details
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ActivityDetails"

    put:
      tags: [Activities]
      summary: Update Activity
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
        - name: activityId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ActivityUpdate"
      responses:
        "200":
          description: Activity updated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Activity"

    delete:
      tags: [Activities]
      summary: Delete Activity
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
        - name: activityId
          in: path
          required: true
          schema:
            type: string
      responses:
        "204":
          description: Activity deleted

  /activities/{activityId}/actions:
    get:
      tags: [Actions]
      summary: Get Activity Actions
      description: Retrieve all actions within a specific activity
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
        - name: activityId
          in: path
          required: true
          schema:
            type: string
            example: "act_browse_product_catalog"
      responses:
        "200":
          description: Actions retrieved
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Action"

    post:
      tags: [Actions]
      summary: Create Action in Activity
      description: Create a new action within a specific activity
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
        - name: activityId
          in: path
          required: true
          schema:
            type: string
            example: "act_browse_product_catalog"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ActionCreate"
      responses:
        "201":
          description: Action created
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Action"

  /actions/{actionId}:
    get:
      tags: [Actions]
      summary: Get Action
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
        - name: actionId
          in: path
          required: true
          schema:
            type: string
            example: "action_open_app"
      responses:
        "200":
          description: Action details
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ActionDetails"

    put:
      tags: [Actions]
      summary: Update Action
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
        - name: actionId
          in: path
          required: true
          schema:
            type: string
            example: "action_open_app"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ActionUpdate"
      responses:
        "200":
          description: Action updated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Action"

    delete:
      tags: [Actions]
      summary: Delete Action
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
        - name: actionId
          in: path
          required: true
          schema:
            type: string
            example: "action_open_app"
      responses:
        "204":
          description: Action deleted

  /personas:
    get:
      tags: [Personas]
      summary: Get Organization Personas
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
      responses:
        "200":
          description: Personas retrieved
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Persona"

    post:
      tags: [Personas]
      summary: Create Persona
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PersonaCreate"
      responses:
        "201":
          description: Persona created
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Persona"

  /personas/type/{personaType}:
    get:
      tags: [Personas]
      summary: Get Personas by Type
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
        - name: personaType
          in: path
          required: true
          schema:
            type: string
            enum: [customer, front_stage, back_stage, system]
          description: The type of personas to retrieve
      responses:
        "200":
          description: Personas of specified type retrieved
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Persona"

  /personas/{personaId}:
    get:
      tags: [Personas]
      summary: Get Persona
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
        - name: personaId
          in: path
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Persona details
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PersonaDetails"

    put:
      tags: [Personas]
      summary: Update Persona
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
        - name: personaId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PersonaUpdate"
      responses:
        "200":
          description: Persona updated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Persona"

    delete:
      tags: [Personas]
      summary: Delete Persona
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
        - name: personaId
          in: path
          required: true
          schema:
            type: string
      responses:
        "204":
          description: Persona deleted

  /components:
    get:
      tags: [Components]
      summary: Get Organization Components
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
      responses:
        "200":
          description: Components retrieved
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Component"

    post:
      tags: [Components]
      summary: Create Component
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ComponentCreate"
      responses:
        "201":
          description: Component created
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Component"

  /components/{componentId}:
    get:
      tags: [Components]
      summary: Get Component
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
        - name: componentId
          in: path
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Component details
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ComponentDetails"

    put:
      tags: [Components]
      summary: Update Component
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
        - name: componentId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ComponentUpdate"
      responses:
        "200":
          description: Component updated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Component"

    delete:
      tags: [Components]
      summary: Delete Component
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
        - name: componentId
          in: path
          required: true
          schema:
            type: string
      responses:
        "204":
          description: Component deleted

  /components/match:
    post:
      tags: [Components]
      summary: Find Matching Components
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [text]
              properties:
                text:
                  type: string
                  example: "Customer completing mobile checkout and payment verification"
                threshold:
                  type: number
                  format: float
                  minimum: 0
                  maximum: 1
                  default: 0.7
      responses:
        "200":
          description: Component matches found
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/ComponentMatch"

  /ai/generate-text:
    post:
      tags: [AI Services]
      summary: Generate AI Text Content
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [content_type, context]
              properties:
                content_type:
                  type: string
                  enum:
                    [
                      customer_goal_description,
                      map_description,
                      phase_description,
                      moment_description,
                      activity_description,
                      action_description,
                      persona_description,
                      persona_goals,
                      persona_frustrations,
                    ]
                context:
                  type: object
                  additionalProperties: true
                tone:
                  type: string
                  enum: [professional, casual, technical, friendly]
                  default: professional
                length:
                  type: string
                  enum: [short, medium, long]
                  default: medium
      responses:
        "200":
          description: AI text generated
          content:
            application/json:
              schema:
                type: object
                properties:
                  generated_text:
                    type: string
                    example: "Customers often feel frustrated when they can't find specific products in the store, especially during busy shopping periods. This moment requires clear signage and helpful staff assistance to guide customers efficiently."
                  metadata:
                    type: object
                    properties:
                      model_used:
                        type: string
                        example: "gpt-4-turbo"
                      token_count:
                        type: integer
                        example: 156

  /ai/generate-image:
    post:
      tags: [AI Services]
      summary: Generate AI Images
      parameters:
        - $ref: "#/components/parameters/OrgOverride"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [image_type, parameters]
              properties:
                image_type:
                  type: string
                  enum: [persona_avatar, map_illustration, phase_icon]
                parameters:
                  type: object
                  additionalProperties: true
                style:
                  type: string
                  enum: [professional, modern, minimalist, colorful]
                  default: professional
      responses:
        "200":
          description: AI image generated
          content:
            application/json:
              schema:
                type: object
                properties:
                  image_url:
                    type: string
                    format: uri
                    example: "https://cdn.acmeretail.com/generated/persona_busy_parent_avatar_2024.png"
                  thumbnail_url:
                    type: string
                    format: uri
                    example: "https://cdn.acmeretail.com/generated/thumbs/persona_busy_parent_avatar_2024_thumb.png"

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: |
        JWT token containing user and organization context.
        Token includes: user_id, organization_id, role, permissions

  parameters:
    OrgOverride:
      name: X-Organization-Id
      in: header
      required: false
      schema:
        type: string
        example: "global-tech-solutions"
      description: |
        Override organization context (Super Admin only).
        Regular users cannot use this header.

  responses:
    Unauthorized:
      description: Authentication required
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Error"

    Forbidden:
      description: Insufficient permissions
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Error"

    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Error"

  schemas:
    Organization:
      type: object
      properties:
        organization_id:
          type: string
          example: "acme-financial-2024"
        name:
          type: string
          example: "Acme Financial Services"
        industry:
          type: string
          example: "Financial Services"
        subscription_tier:
          type: string
          enum: [starter, professional, enterprise]
        settings:
          type: object
          properties:
            timezone:
              type: string
              example: "America/New_York"
            locale:
              type: string
              example: "en-US"
            data_retention_days:
              type: integer
              example: 2555
        status:
          type: string
          enum: [active, suspended, archived]
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    OrganizationDetails:
      allOf:
        - $ref: "#/components/schemas/Organization"
        - type: object
          properties:
            business_structure:
              type: object
              properties:
                divisions:
                  type: array
                  items:
                    type: string
                channels:
                  type: array
                  items:
                    type: string
                products:
                  type: array
                  items:
                    type: string
                brands:
                  type: array
                  items:
                    type: string
            statistics:
              type: object
              properties:
                total_users:
                  type: integer
                total_maps:
                  type: integer
                total_personas:
                  type: integer

    OrganizationCreate:
      type: object
      required: [name, industry]
      properties:
        name:
          type: string
          example: "Global Tech Solutions Inc."
        industry:
          type: string
          example: "Technology Services"
        subscription_tier:
          type: string
          enum: [starter, professional, enterprise]
          default: starter
          example: "professional"
        settings:
          type: object
          properties:
            timezone:
              type: string
              example: "America/Los_Angeles"
            locale:
              type: string
              example: "en-US"

    OrganizationUpdate:
      type: object
      properties:
        name:
          type: string
        industry:
          type: string
        settings:
          type: object
          properties:
            timezone:
              type: string
            locale:
              type: string
        business_structure:
          type: object
          properties:
            divisions:
              type: array
              items:
                type: string
            channels:
              type: array
              items:
                type: string

    UserProfile:
      type: object
      properties:
        user_id:
          type: string
          example: "usr_sarah_johnson_001"
        organization_id:
          type: string
          example: "acme-financial-2024"
        profile:
          type: object
          properties:
            email:
              type: string
              format: email
              example: "<EMAIL>"
            first_name:
              type: string
              example: "Sarah"
            last_name:
              type: string
              example: "Johnson"
            display_name:
              type: string
              example: "Sarah J."
            avatar_url:
              type: string
              format: uri
              example: "https://cdn.acmefinancial.com/avatars/sarah_johnson.jpg"
        access_control:
          type: object
          properties:
            role:
              type: string
              enum: [admin, writer, reader]
            department:
              type: string
            permissions:
              type: array
              items:
                type: string

    User:
      type: object
      properties:
        user_id:
          type: string
          example: "usr_mike_chen_005"
        organization_id:
          type: string
          example: "acme-financial-2024"
        profile:
          type: object
          properties:
            email:
              type: string
              format: email
            first_name:
              type: string
            last_name:
              type: string
            display_name:
              type: string
            avatar_url:
              type: string
              format: uri
        access_control:
          type: object
          properties:
            role:
              type: string
              enum: [admin, writer, reader]
            department:
              type: string
            permissions:
              type: array
              items:
                type: string
        created_by:
          $ref: "#/components/schemas/UserReference"
        updated_by:
          $ref: "#/components/schemas/UserReference"

    UserDetails:
      allOf:
        - $ref: "#/components/schemas/User"
        - type: object
          properties:
            recent_activity:
              type: array
              items:
                type: object
                properties:
                  action:
                    type: string
                  entity_type:
                    type: string
                  entity_id:
                    type: string
                  timestamp:
                    type: string
                    format: date-time

    UserCreate:
      type: object
      required: [email, first_name, last_name, role]
      properties:
        email:
          type: string
          format: email
          example: "<EMAIL>"
        first_name:
          type: string
          example: "Michael"
        last_name:
          type: string
          example: "Chen"
        display_name:
          type: string
          example: "Mike C."
        role:
          type: string
          enum: [admin, writer, reader]
          example: "writer"
        department:
          type: string
          example: "Product Management"
        permissions:
          type: array
          items:
            type: string
          example: ["create_maps", "edit_personas", "view_reports"]

    UserUpdate:
      type: object
      properties:
        profile:
          type: object
          properties:
            first_name:
              type: string
            last_name:
              type: string
            display_name:
              type: string
            avatar_url:
              type: string
              format: uri
        access_control:
          type: object
          properties:
            role:
              type: string
              enum: [admin, writer, reader]
            department:
              type: string
            permissions:
              type: array
              items:
                type: string

    CustomerGoal:
      type: object
      properties:
        customer_goal_id:
          type: string
          example: "cg_complete_shopping_2024"
        organization_id:
          type: string
          example: "acme-retail-group"
        name:
          type: string
          example: "Complete Weekly Grocery Shopping"
        description:
          type: string
          example: "Successfully purchase all needed groceries for the week with minimal time and effort"
        category:
          type: string
          example: "Shopping & Commerce"
        priority:
          type: string
          enum: [high, medium, low]
          example: "high"
        status:
          type: string
          enum: [active, draft, archived]
          example: "active"
        created_by:
          $ref: "#/components/schemas/UserReference"
        updated_by:
          $ref: "#/components/schemas/UserReference"

    CustomerGoalDetails:
      allOf:
        - $ref: "#/components/schemas/CustomerGoal"
        - type: object
          properties:
            maps:
              type: array
              items:
                type: object
                properties:
                  map_id:
                    type: string
                  name:
                    type: string
                  state:
                    type: string

    CustomerGoalCreate:
      type: object
      required: [name, description]
      properties:
        name:
          type: string
          example: "Complete Online Grocery Ordering"
        description:
          type: string
          example: "Customer successfully places and receives an online grocery order with all desired items"
        category:
          type: string
          example: "E-commerce & Delivery"
        priority:
          type: string
          enum: [high, medium, low]
          default: medium
          example: "high"

    CustomerGoalUpdate:
      type: object
      properties:
        name:
          type: string
        description:
          type: string
        category:
          type: string
        priority:
          type: string
          enum: [high, medium, low]
        status:
          type: string
          enum: [active, draft, archived]

    Map:
      type: object
      properties:
        map_id:
          type: string
          example: "map_grocery_journey_v3"
        organization_id:
          type: string
          example: "acme-retail-group"
        customer_goal:
          $ref: "#/components/schemas/CustomerGoal"
        name:
          type: string
          example: "In-Store Shopping Experience"
        description:
          type: string
          example: "Complete journey mapping for customers shopping in physical grocery stores"
        division:
          type: string
          example: "Retail Operations"
        state:
          type: string
          enum: [draft, review, published, archived]
          example: "published"
        status:
          type: string
          enum: [active, inactive]
          example: "active"
        created_by:
          $ref: "#/components/schemas/UserReference"
        updated_by:
          $ref: "#/components/schemas/UserReference"

    MapDetails:
      allOf:
        - $ref: "#/components/schemas/Map"
        - type: object
          properties:
            phases:
              type: array
              items:
                allOf:
                  - $ref: "#/components/schemas/Phase"
                  - type: object
                    properties:
                      moment_ids:
                        type: array
                        items:
                          type: string
                        description: "Array of moment IDs that belong to this phase"
                        example: ["moment_compare_prices_online", "moment_check_product_reviews"]
            moments:
              type: array
              items:
                $ref: "#/components/schemas/MomentDetails"
              description: "All moments in the map (deduplicated - shared moments appear only once)"
            personas:
              type: array
              items:
                $ref: "#/components/schemas/PersonaDetails"
              description: "All personas referenced in the map (deduplicated)"
            components:
              type: array
              items:
                $ref: "#/components/schemas/ComponentDetails"
              description: "All components referenced in the map (deduplicated)"
            statistics:
              type: object
              properties:
                total_phases:
                  type: integer
                total_moments:
                  type: integer
                total_actions:
                  type: integer
                total_personas:
                  type: integer
                total_components:
                  type: integer

    MapCreate:
      type: object
      required: [name, description, customer_goal_id, division]
      properties:
        name:
          type: string
          example: "Mobile App Shopping Journey"
        description:
          type: string
          example: "End-to-end customer journey for mobile app grocery shopping experience"
        customer_goal_id:
          type: string
          example: "cg_complete_shopping_2024"
        division:
          type: string
          example: "Digital Commerce"
        state:
          type: string
          enum: [draft, review, published]
          default: draft
          example: "draft"

    MapUpdate:
      type: object
      properties:
        name:
          type: string
        description:
          type: string
        division:
          type: string
        state:
          type: string
          enum: [draft, review, published, archived]
        status:
          type: string
          enum: [active, inactive]

    Phase:
      type: object
      properties:
        phase_id:
          type: string
          example: "phase_research_products"
        organization_id:
          type: string
          example: "acme-retail-group"
        map_id:
          type: string
          example: "map_grocery_journey_v3"
        name:
          type: string
          example: "Product Research & Planning"
        description:
          type: string
          example: "Customer researches products and plans their shopping trip"
        sequence:
          type: integer
          example: 1
        components:
          type: array
          items:
            type: string
          example: ["comp_meal_planning_001", "comp_price_comparison_002"]
        status:
          type: string
          enum: [active, inactive, archived]
          example: "active"
        created_by:
          $ref: "#/components/schemas/UserReference"
        updated_by:
          $ref: "#/components/schemas/UserReference"

    PhaseDetails:
      allOf:
        - $ref: "#/components/schemas/Phase"
        - type: object
          properties:
            moment_ids:
              type: array
              items:
                type: string
              description: "Array of moment IDs that belong to this phase"
              example: ["moment_compare_prices_online", "moment_check_product_reviews"]

    PhaseCreate:
      type: object
      required: [name, description]
      properties:
        name:
          type: string
          example: "Pre-Shopping Preparation"
        description:
          type: string
          example: "Customer prepares for shopping by creating lists and checking for deals"
        sequence:
          type: integer
          minimum: 1
          example: 1
        components:
          type: array
          items:
            type: string
          example: ["comp_shopping_list_creation", "comp_coupon_discovery"]

    PhaseUpdate:
      type: object
      properties:
        name:
          type: string
        description:
          type: string
        sequence:
          type: integer
        components:
          type: array
          items:
            type: string
        status:
          type: string
          enum: [active, inactive, archived]

    Moment:
      type: object
      properties:
        moment_id:
          type: string
          example: "moment_compare_prices_online"
        organization_id:
          type: string
          example: "acme-retail-group"
        phase_id:
          type: string
          example: "phase_research_products"
        name:
          type: string
          example: "Compare Prices Online"
        description:
          type: string
          example: "Customer compares prices across different platforms"
        image_url:
          type: string
          format: uri
          example: "https://cdn.acmeretail.com/moments/compare_prices_online.jpg"
        sequence:
          type: integer
          minimum: 1
          example: 2
        components:
          type: array
          items:
            type: string
          example: ["comp_price_comparison_002", "comp_mobile_app_003"]
        status:
          type: string
          enum: [active, inactive, archived]
          example: "active"
        created_by:
          $ref: "#/components/schemas/UserReference"
        updated_by:
          $ref: "#/components/schemas/UserReference"

    MomentDetails:
      allOf:
        - $ref: "#/components/schemas/Moment"
        - type: object
          properties:
            activities:
              $ref: "#/components/schemas/ActivitiesByType"
              description: "Activities grouped by activity type with full details including actions (customer, front_stage, back_stage, system)"

    MomentCreate:
      type: object
      required: [name, description]
      properties:
        name:
          type: string
          example: "Check Product Availability"
        description:
          type: string
          example: "Customer checks if desired products are available in-store or online"
        image_url:
          type: string
          format: uri
          example: "https://cdn.acmeretail.com/moments/check_product_availability.jpg"
        sequence:
          type: integer
          minimum: 1
          example: 3
        components:
          type: array
          items:
            type: string
          example: ["comp_inventory_check", "comp_store_locator"]

    MomentUpdate:
      type: object
      properties:
        name:
          type: string
        description:
          type: string
        image_url:
          type: string
          format: uri
        sequence:
          type: integer
          minimum: 1
        components:
          type: array
          items:
            type: string
        status:
          type: string
          enum: [active, inactive, archived]

    Activity:
      type: object
      properties:
        activity_id:
          type: string
          example: "act_browse_product_catalog"
        organization_id:
          type: string
          example: "acme-retail-group"
        moment_id:
          type: string
          example: "moment_compare_prices_online"
        name:
          type: string
          example: "Browse Product Catalog"
        description:
          type: string
          example: "Customer browses through available products using the mobile app"
        activity_type:
          type: string
          enum: [customer, front_stage, back_stage, system]
          example: "customer"
          description: "The type of activity for grouping purposes"
        estimated_duration:
          type: string
          example: "3-5 minutes"
        complexity_level:
          type: string
          enum: [low, medium, high]
          example: "medium"
        personas:
          type: array
          items:
            type: string
          description: Array of persona IDs associated with this activity
          example:
            ["persona_busy_parent_shopper", "persona_tech_savvy_millennial"]
        sequence:
          type: integer
          minimum: 1
          example: 1
          description: "Order of this activity within its activity type"
        status:
          type: string
          enum: [active, inactive, archived]
          example: "active"
        created_by:
          $ref: "#/components/schemas/UserReference"
        updated_by:
          $ref: "#/components/schemas/UserReference"

    ActivityDetails:
      allOf:
        - $ref: "#/components/schemas/Activity"
        - type: object
          properties:
            action_details:
              type: array
              items:
                $ref: "#/components/schemas/Action"
              description: "Full action objects with all details (expands the action IDs from the base activity)"

    ActivityCreate:
      type: object
      required: [name, description, activity_type]
      properties:
        name:
          type: string
          example: "Scan Product Barcode"
        description:
          type: string
          example: "Customer uses mobile app to scan product barcode for information"
        activity_type:
          type: string
          enum: [customer, front_stage, back_stage, system]
          example: "customer"
        estimated_duration:
          type: string
          example: "30 seconds"
        complexity_level:
          type: string
          enum: [low, medium, high]
          default: medium
          example: "low"
        personas:
          type: array
          items:
            type: string
          description: Array of persona IDs associated with this activity
          example:
            ["persona_busy_parent_shopper", "persona_tech_savvy_millennial"]
        sequence:
          type: integer
          minimum: 1
          example: 1
          description: "Order of this activity within its activity type"

    ActivityUpdate:
      type: object
      properties:
        name:
          type: string
        description:
          type: string
        activity_type:
          type: string
          enum: [customer, front_stage, back_stage, system]
        estimated_duration:
          type: string
        complexity_level:
          type: string
          enum: [low, medium, high]
        personas:
          type: array
          items:
            type: string
          description: Array of persona IDs associated with this activity
        sequence:
          type: integer
          minimum: 1
          description: "Order of this activity within its activity type"
        status:
          type: string
          enum: [active, inactive, archived]

    Action:
      type: object
      properties:
        action_id:
          type: string
          example: "action_add_to_cart_flow"
        organization_id:
          type: string
          example: "acme-retail-group"
        activity_id:
          type: string
          example: "act_browse_product_catalog"
          description: "ID of the activity this action belongs to"
        name:
          type: string
          example: "Add Item to Shopping Cart"
        description:
          type: string
          example: "Customer adds selected product to their digital shopping cart"
        sequence:
          type: integer
          minimum: 1
          example: 1
          description: "Order of this action within the activity"
        category:
          type: string
          example: "E-commerce Interaction"
        link:
          type: string
          format: uri
          example: "https://acmeretail.atlassian.net/browse/CART-123"
        supported_by:
          type: string
          example: "Development Team Alpha"
        reference_screen:
          type: string
          example: "Shopping Cart Interface v2.1"
        delivery_epic:
          type: string
          example: "E-commerce Enhancement Q1 2024"
        additional_document_references:
          type: array
          items:
            type: string
          example: ["https://acmeretail.confluence.com/design-doc-123", "https://acmeretail.sharepoint.com/specs/cart-requirements"]
        priority:
          type: string
          enum: [high, medium, low]
          example: "high"
        estimated_effort:
          type: string
          example: "2 story points"
        dependencies:
          type: array
          items:
            type: string
          example: ["action_product_selection", "action_user_authentication"]
        usage_count:
          type: integer
          example: 47
        last_used:
          type: string
          format: date-time
          example: "2024-01-15T10:30:00Z"
        impact_score:
          type: number
          format: float
          example: 8.5
        status:
          type: string
          enum: [active, inactive, archived]
          example: "active"
        created_by:
          $ref: "#/components/schemas/UserReference"
        updated_by:
          $ref: "#/components/schemas/UserReference"

    ActionDetails:
      allOf:
        - $ref: "#/components/schemas/Action"

    ActionCreate:
      type: object
      required: [name, description, sequence, category]
      properties:
        name:
          type: string
          example: "Send Push Notification for Deal"
        description:
          type: string
          example: "System sends personalized push notification to customer about relevant deals"
        sequence:
          type: integer
          minimum: 1
          example: 1
          description: "Order of this action within the activity"
        category:
          type: string
          example: "Marketing & Notifications"
        link:
          type: string
          format: uri
          example: "https://acmeretail.atlassian.net/browse/NOTIF-456"
        supported_by:
          type: string
          example: "Mobile Development Team"
        reference_screen:
          type: string
          example: "Push Notification Settings Screen"
        delivery_epic:
          type: string
          example: "Customer Engagement Platform Q2 2024"
        additional_document_references:
          type: array
          items:
            type: string
          example: ["https://acmeretail.confluence.com/notification-strategy", "https://acmeretail.sharepoint.com/mobile-ux-guidelines"]
        priority:
          type: string
          enum: [high, medium, low]
          default: medium
          example: "medium"
        estimated_effort:
          type: string
          example: "1 story point"
        dependencies:
          type: array
          items:
            type: string
          example:
            [
              "action_customer_location_check",
              "action_deal_availability_verify",
            ]

    ActionUpdate:
      type: object
      properties:
        name:
          type: string
        description:
          type: string
        sequence:
          type: integer
          minimum: 1
          description: "Order of this action within the activity"
        category:
          type: string
        link:
          type: string
          format: uri
        supported_by:
          type: string
        reference_screen:
          type: string
        delivery_epic:
          type: string
        additional_document_references:
          type: array
          items:
            type: string
        priority:
          type: string
          enum: [high, medium, low]
        estimated_effort:
          type: string
        dependencies:
          type: array
          items:
            type: string
        status:
          type: string
          enum: [active, inactive, archived]

    Persona:
      type: object
      properties:
        persona_id:
          type: string
          example: "persona_busy_parent_shopper"
        organization_id:
          type: string
          example: "acme-retail-group"
        name:
          type: string
          example: "Emily Rodriguez - Busy Working Parent"
        persona_type:
          type: string
          enum: [customer, front_stage, back_stage, system]
          example: "customer"
        # Human persona fields
        image_url:
          type: string
          format: uri
          example: "https://cdn.acmeretail.com/personas/emily_rodriguez.jpg"
        tagline:
          type: string
          example: "Efficient shopping for a busy family"
        age:
          type: integer
          example: 34
        role:
          type: string
          example: "Marketing Manager & Mother of Two"
        income:
          type: string
          example: "$75,000 - $95,000"
        location:
          type: string
          example: "Austin, Texas"
        status_description:
          type: string
          example: "Active professional balancing career and family responsibilities"
        motivations:
          type: array
          items:
            type: string
          example:
            [
              "Save time on grocery shopping",
              "Provide healthy meals for family",
              "Stay within budget",
            ]
        frustrations:
          type: array
          items:
            type: string
          example:
            [
              "Long checkout lines",
              "Out-of-stock items",
              "Difficult store navigation",
            ]
        goals:
          type: array
          items:
            type: string
          example:
            [
              "Complete shopping in under 30 minutes",
              "Find all items on shopping list",
              "Stay within weekly budget",
            ]
        preferences:
          type: array
          items:
            type: string
          example:
            [
              "Mobile app notifications for deals",
              "Self-checkout options",
              "Organized store layout",
            ]
        # System persona fields (when persona_type = "system")
        description:
          type: string
          example: "Cloud-based inventory management system that tracks product availability in real-time"
        category:
          type: string
          example: "Inventory Management"
        vendor:
          type: string
          example: "Oracle Retail"
        platform:
          type: string
          example: "Oracle Cloud Infrastructure"
        availability:
          type: string
          example: "99.9% uptime SLA"
        # Common fields
        notes:
          type: string
          example: "Primary system for real-time inventory tracking across all store locations"
        usage_frequency:
          type: integer
          example: 24
        status:
          type: string
          enum: [active, inactive, maintenance, deprecated, archived]
          example: "active"
        created_by:
          $ref: "#/components/schemas/UserReference"
        updated_by:
          $ref: "#/components/schemas/UserReference"

    PersonaDetails:
      allOf:
        - $ref: "#/components/schemas/Persona"
        - type: object
          properties:
            moments_used:
              type: array
              items:
                type: object
                properties:
                  moment_id:
                    type: string
                  moment_name:
                    type: string
                  map_name:
                    type: string
                  activity_count:
                    type: integer
                    description: "Number of activities this persona is involved in for this moment"

    PersonaCreate:
      type: object
      required: [name, persona_type]
      properties:
        name:
          type: string
        persona_type:
          type: string
          enum: [customer, front_stage, back_stage, system]
        # Human persona fields (required for non-system personas)
        image_url:
          type: string
          format: uri
        tagline:
          type: string
        age:
          type: integer
          minimum: 1
          maximum: 120
        role:
          type: string
        income:
          type: string
        location:
          type: string
        status_description:
          type: string
        motivations:
          type: array
          items:
            type: string
        frustrations:
          type: array
          items:
            type: string
        goals:
          type: array
          items:
            type: string
        preferences:
          type: array
          items:
            type: string
        # System persona fields (required for system personas)
        description:
          type: string
        category:
          type: string
        vendor:
          type: string
        platform:
          type: string
        availability:
          type: string
        # Common fields
        notes:
          type: string

    PersonaUpdate:
      type: object
      properties:
        name:
          type: string
        persona_type:
          type: string
          enum: [customer, front_stage, back_stage, system]
        # Human persona fields
        image_url:
          type: string
          format: uri
        tagline:
          type: string
        age:
          type: integer
        role:
          type: string
        income:
          type: string
        location:
          type: string
        status_description:
          type: string
        motivations:
          type: array
          items:
            type: string
        frustrations:
          type: array
          items:
            type: string
        goals:
          type: array
          items:
            type: string
        preferences:
          type: array
          items:
            type: string
        # System persona fields
        description:
          type: string
        category:
          type: string
        vendor:
          type: string
        platform:
          type: string
        availability:
          type: string
        # Common fields
        notes:
          type: string
        status:
          type: string
          enum: [active, inactive, maintenance, deprecated, archived]

    Component:
      type: object
      properties:
        component_id:
          type: string
          example: "comp_payment_processing_001"
        organization_id:
          type: string
          example: "acme-retail-group"
        numbering:
          type: string
          example: "PAY-001"
        name:
          type: string
          example: "Secure Payment Processing"
        description:
          type: string
          example: "End-to-end secure payment processing for credit cards and digital wallets"
        best_practices:
          type: string
          example: "Always validate payment information before processing. Implement PCI DSS compliance standards."
        count:
          type: integer
          example: 12
        level:
          type: string
          example: "Critical"
        framework:
          type: string
          enum: [customer, business]
          example: "customer"
        phases_used:
          type: array
          items:
            type: string
          example: ["phase_checkout_process", "phase_order_completion"]
        moments_used:
          type: array
          items:
            type: string
          example: ["moment_enter_payment_info", "moment_confirm_purchase"]
        status:
          type: string
          enum: [active, inactive, archived]
          example: "active"
        created_by:
          $ref: "#/components/schemas/UserReference"
        updated_by:
          $ref: "#/components/schemas/UserReference"

    ComponentDetails:
      allOf:
        - $ref: "#/components/schemas/Component"
        - type: object
          properties:
            usage_details:
              type: array
              items:
                type: object
                properties:
                  entity_type:
                    type: string
                    enum: [phase, moment]
                  entity_id:
                    type: string
                  entity_name:
                    type: string
                  map_name:
                    type: string

    ComponentCreate:
      type: object
      required: [numbering, name, description]
      properties:
        numbering:
          type: string
          example: "INV-002"
        name:
          type: string
          example: "Real-time Inventory Sync"
        description:
          type: string
          example: "Synchronizes inventory levels across all channels in real-time"
        best_practices:
          type: string
          example: "Update inventory every 30 seconds. Alert when stock falls below threshold."
        count:
          type: integer
          example: 8
        level:
          type: string
          example: "Essential"
        framework:
          type: string
          enum: [customer, business]
          example: "business"

    ComponentUpdate:
      type: object
      properties:
        numbering:
          type: string
        name:
          type: string
        description:
          type: string
        best_practices:
          type: string
        count:
          type: integer
        level:
          type: string
        framework:
          type: string
          enum: [customer, business]
        status:
          type: string
          enum: [active, inactive, archived]

    ComponentMatch:
      type: object
      properties:
        component_id:
          type: string
          example: "comp_payment_processing_001"
        component_name:
          type: string
          example: "Secure Payment Processing"
        numbering:
          type: string
          example: "PAY-001"
        similarity_score:
          type: number
          format: float
          minimum: 0
          maximum: 1
          example: 0.92
        description:
          type: string
          example: "End-to-end secure payment processing for credit cards and digital wallets"

    UserReference:
      type: object
      properties:
        user_id:
          type: string
          example: "usr_sarah_johnson_001"
        name:
          type: string
          example: "Sarah Johnson"
        profile_image_url:
          type: string
          format: uri
          example: "https://cdn.acmefinancial.com/avatars/sarah_johnson.jpg"
        timestamp:
          type: string
          format: date-time
          example: "2024-01-15T14:30:00Z"

    Error:
      type: object
      properties:
        error:
          type: string
          example: "validation_failed"
        code:
          type: string
          example: "VAL_001"
        message:
          type: string
          example: "Required field 'name' is missing from the request"
        timestamp:
          type: string
          format: date-time
          example: "2024-01-15T14:30:00Z"

    ActivitiesByType:
      type: object
      properties:
        customer:
          type: array
          items:
            $ref: "#/components/schemas/ActivityDetails"
        front_stage:
          type: array
          items:
            $ref: "#/components/schemas/ActivityDetails"
        back_stage:
          type: array
          items:
            $ref: "#/components/schemas/ActivityDetails"
        system:
          type: array
          items:
            $ref: "#/components/schemas/ActivityDetails"

tags:
  - name: Authentication
    description: User authentication and session management
  - name: Super Admin
    description: Organization management for super admin users
  - name: Organization
    description: Current organization management
  - name: Users
    description: User management within current organization
  - name: Customer Goals
    description: Strategic customer objectives management
  - name: Maps
    description: Customer map creation and management
  - name: Phases
    description: Map phase management
  - name: Moments
    description: Granular map moment management
  - name: Activities
    description: Activity management within moments, grouped by activity type (customer, front_stage, back_stage, system)
  - name: Actions
    description: Action management within activities - actions are created and managed as part of activities
  - name: Personas
    description: Detailed persona profile management (includes human personas and system personas)
  - name: Components
    description: Cross-cutting component management and analysis
  - name: AI Services
    description: AI-powered content generation and assistance